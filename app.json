{"name": "<PERSON><PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "expo": {"name": "<PERSON><PERSON><PERSON><PERSON>", "slug": "<PERSON><PERSON><PERSON><PERSON>", "scheme": "fishreader", "version": "1.0.0", "orientation": "portrait", "userInterfaceStyle": "automatic", "icon": "./assets/images/logo.png", "splash": {"image": "./assets/images/logo.png", "resizeMode": "contain", "backgroundColor": "#308B3C"}, "updates": {"fallbackToCacheTimeout": 0}, "newArchEnabled": false, "jsEngine": "hermes", "assetBundlePatterns": ["**/*"], "android": {"icon": "./assets/images/logo.png", "package": "com.fishreader", "adaptiveIcon": {"foregroundImage": "./assets/images/logo.png", "backgroundColor": "#308B3C"}, "splash": {"image": "./assets/images/logo.png", "resizeMode": "contain", "backgroundColor": "#308B3C"}}, "ios": {"icon": "./assets/images/logo.png", "supportsTablet": true, "bundleIdentifier": "com.fishreader", "splash": {"image": "./assets/images/logo.png", "resizeMode": "contain", "backgroundColor": "#308B3C"}}, "web": {"favicon": "./assets/images/logo.png", "bundler": "metro"}, "plugins": ["expo-localization", "expo-font", ["expo-splash-screen", {"image": "./assets/images/logo.png", "resizeMode": "contain", "backgroundColor": "#308B3C"}]], "experiments": {"tsconfigPaths": true}}, "ignite": {"version": "10.1.6"}}