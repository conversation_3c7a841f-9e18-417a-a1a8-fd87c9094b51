# flow: run the login flow and then navigate to the demo podcast list screen, favorite a podcast, and then switch the list to only be favorites.

appId: com.fishreader
env:
  FAVORITES_TEXT: "Switch on to only show favorites"

---
- runFlow: Login.yaml
- tapOn: "Podcast"
- assertVisible: "React Native Radio episodes"
- tapOn:
    text: ${FAVORITES_TEXT}
- assertVisible: "This looks a bit empty"
- tapOn:
    text: ${FAVORITES_TEXT}
    # https://maestro.mobile.dev/troubleshooting/known-issues#android-accidental-double-tap
    retryTapIfNoChange: false
- repeat:
    times: 2
    commands:
      - scroll
- copyTextFrom:
    text: "^RNR.*" # assumes all podcast titles start with RNR
    index: 1 # grab the second one, the first one might not be fully visible
- longPressOn: ${maestro.copiedText}
- scrollUntilVisible:
    element:
      text: ${FAVORITES_TEXT}
    direction: UP
    timeout: 50000
    speed: 40
    visibilityPercentage: 100
- tapOn:
    text: ${FAVORITES_TEXT}
- assertVisible: ${maestro.copiedText}

