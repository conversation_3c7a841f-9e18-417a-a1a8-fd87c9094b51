# Product Overview

Fish Reader is a React Native mobile application built with Expo. The app appears to be a reading or content consumption platform with user authentication, profile management, and client-focused features.

## Key Features

- User authentication (login, registration, password reset)
- Email verification system
- User profile management with photo selection
- Client overview and monthly tracking screens
- Multi-language support (English and Turkish)
- Dark/light theme support
- Onboarding flow with multiple launch screens

## Target Platforms

- iOS (native and simulator builds)
- Android (native and simulator builds)
- Web (via Expo web)

## Architecture

The app follows a modern React Native architecture with:
- MobX State Tree for state management
- React Navigation for routing
- Expo managed workflow
- TypeScript for type safety
- Component-based architecture with reusable UI components