# Project Structure

## Root Directory Organization

```
├── app/                    # Main application code
├── assets/                 # Static assets (images, icons)
├── android/               # Android native code
├── ios/                   # iOS native code
├── ignite/                # Ignite CLI templates
├── test/                  # Test configuration and utilities
├── .expo/                 # Expo build artifacts
├── .maestro/              # E2E test files
└── node_modules/          # Dependencies
```

## App Directory Structure (`/app`)

### Core Application Files
- `app.tsx` - Main app entry point with providers and initialization
- `index.tsx` - Root index file

### Feature Directories

#### `/components` - Reusable UI Components
- All components export from `index.ts`
- Follow PascalCase naming convention
- Include TypeScript interfaces for props
- Use themed styling with `useAppTheme` hook
- Examples: `Button.tsx`, `Text.tsx`, `Screen.tsx`

#### `/screens` - Screen Components
- Organized by feature/flow in subdirectories
- Export all screens from `index.ts`
- Screen names end with "Screen" suffix
- Examples: `Login/LoginScreen.tsx`, `Profile/ProfileScreen.tsx`

#### `/navigators` - Navigation Configuration
- `AppNavigator.tsx` - Main navigation setup
- `navigationUtilities.ts` - Navigation helpers
- Uses React Navigation v6 with TypeScript

#### `/models` - MobX State Tree Models
- `RootStore.ts` - Root store configuration
- Individual store files (e.g., `UserStore.ts`, `AuthenticationStore.ts`)
- `/helpers` - Store utilities and hooks

#### `/services` - External Services
- `/api` - API client and types
- Service layer for external integrations

#### `/theme` - Design System
- `colors.ts` / `colorsDark.ts` - Color palettes
- `spacing.ts` / `spacingDark.ts` - Spacing scales
- `typography.ts` - Font definitions
- `index.ts` - Theme exports and types
- Supports light/dark themes

#### `/i18n` - Internationalization
- Language files: `en.ts`, `tr.ts`
- `i18n.ts` - Configuration
- `translate.ts` - Translation utilities

#### `/utils` - Utility Functions
- `/storage` - Storage abstractions
- Helper functions and hooks
- Platform-specific implementations

#### `/config` - Configuration
- Environment-specific configs
- `config.base.ts`, `config.dev.ts`, `config.prod.ts`

#### `/devtools` - Development Tools
- Reactotron configuration
- Development-only utilities

## Assets Organization (`/assets`)

```
assets/
├── icons/                 # App icons and UI icons
│   ├── demo/             # Demo/example icons
│   └── *.png             # Icon files with @2x, @3x variants
└── images/               # Images and graphics
    ├── demo/             # Demo/example images
    └── *.png             # Image files with @2x, @3x variants
```

## Naming Conventions

### Files and Directories
- **Components**: PascalCase (e.g., `Button.tsx`, `UserProfile.tsx`)
- **Screens**: PascalCase with "Screen" suffix (e.g., `LoginScreen.tsx`)
- **Utilities**: camelCase (e.g., `formatDate.ts`, `useAppTheme.ts`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `API_ENDPOINTS.ts`)

### Code Conventions
- **Interfaces**: PascalCase with descriptive names (e.g., `ButtonProps`, `UserData`)
- **Types**: PascalCase (e.g., `ThemeContexts`, `AppStackParamList`)
- **Functions**: camelCase (e.g., `handleLogin`, `formatCurrency`)
- **Variables**: camelCase (e.g., `currentUser`, `isLoading`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `MAX_RETRY_ATTEMPTS`)

## Import Conventions

### Path Aliases
- `@/*` - Maps to `./app/*`
- `assets/*` - Maps to `./assets/*`

### Import Order
1. React and React Native imports
2. Third-party library imports
3. Internal imports using path aliases
4. Relative imports

### Export Patterns
- Use barrel exports (`index.ts`) for components and screens
- Export interfaces alongside components
- Use named exports over default exports where possible

## File Organization Rules

1. **Single Responsibility**: Each file should have a single, clear purpose
2. **Co-location**: Keep related files close together
3. **Barrel Exports**: Use `index.ts` files to create clean import paths
4. **Type Safety**: Include TypeScript interfaces for all props and data structures
5. **Theme Integration**: Use themed styling system for consistent design