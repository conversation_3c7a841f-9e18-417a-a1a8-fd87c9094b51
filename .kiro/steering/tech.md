# Technology Stack

## Core Framework
- **React Native**: 0.76.6
- **Expo**: ~52.0.7 (managed workflow)
- **TypeScript**: ~5.3.3
- **Node.js**: ^18.18.0 || >=20.0.0

## State Management
- **MobX**: 6.10.2
- **MobX State Tree**: 5.3.0
- **MobX React Lite**: 4.0.5

## Navigation
- **React Navigation**: v6
  - Native Stack Navigator
  - Bottom Tabs Navigator
  - Drawer Layout

## UI & Styling
- **React Native Reanimated**: ~3.16.1
- **React Native Gesture Handler**: ~2.20.2
- **Expo Linear Gradient**: ~14.0.2
- **Expo Vector Icons**: ^14.0.2
- **Space Grotesk Font**: Custom font loading

## Storage & Persistence
- **React Native MMKV**: ^2.12.2 (fast key-value storage)
- **Async Storage**: ^2.1.1 (fallback storage)

## Internationalization
- **i18next**: ^23.14.0
- **react-i18next**: ^15.0.1
- **expo-localization**: ~16.0.0

## Development Tools
- **Reactotron**: Development debugging
- **ESLint**: Code linting with Expo config
- **Prettier**: Code formatting
- **Jest**: Testing framework
- **Maestro**: E2E testing

## Build System
- **EAS Build**: Expo Application Services
- **Metro**: React Native bundler
- **Babel**: JavaScript compiler

## Common Commands

### Development
```bash
npm start                    # Start Expo dev server
npm run ios                  # Run on iOS simulator
npm run android             # Run on Android emulator
npm run web                 # Run on web browser
```

### Building
```bash
npm run build:ios:sim       # Build for iOS simulator
npm run build:ios:dev       # Build for iOS device (development)
npm run build:ios:prod      # Build for iOS device (production)
npm run build:android:sim   # Build for Android emulator
npm run build:android:dev   # Build for Android device (development)
npm run build:android:prod  # Build for Android device (production)
```

### Code Quality
```bash
npm run lint                # Fix linting issues
npm run lint:check          # Check for linting issues
npm run compile             # TypeScript compilation check
npm test                    # Run Jest tests
npm run test:watch          # Run tests in watch mode
npm run test:maestro        # Run E2E tests
```

### Utilities
```bash
npm run adb                 # Set up Android port forwarding
npm run prebuild:clean      # Clean Expo prebuild
```