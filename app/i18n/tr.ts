const tr = {
  common: {
    ok: "<PERSON><PERSON>",
    cancel: "<PERSON><PERSON><PERSON>",
    back: "<PERSON><PERSON>",
    backWithArrow: "< Geri",
    error: "<PERSON><PERSON>",
    logOut: "<PERSON><PERSON><PERSON><PERSON><PERSON> Yap",
    close: "<PERSON><PERSON><PERSON>",
    save: "<PERSON><PERSON>",
    edit: "<PERSON><PERSON><PERSON><PERSON>",
  },
  welcomeScreen: {
    postscript:
      "Not — Uygulamanız muhtemelen böyle görünmüyor. (Tasarımcınız size bu ekranları verdiyse, o zaman yayınlayın!)",
    readyForLaunch: "Uygulamanız, yayına hazır!",
    exciting: "(ahh, bu heyecan verici!)",
    letsGo: "Hadi başlayalım!",
  },
  emptyStateComponent: {
    generic: {
      heading: "Çok boş... çok üzücü",
      content: "Henüz veri bulunamadı. Yenilemek için düğmeye tıklayın veya uygulamayı yeniden yükleyin.",
      button: "<PERSON><PERSON><PERSON> deneye<PERSON>",
    },
  },
  "onboarding": {
    "uploadPhotos": {
      "title": "Fotoğraf <PERSON>",
      "description": "Fotoğraflarınızı sisteme yükleyin."
    },
    "processImages": {
      "title": "Görselleri İşle",
      "description": "Görsellerinizi otomatik olarak işleyeceğiz."
    },
    "viewResults": {
      "title": "Sonuçları Görüntüle",
      "description": "Yüklemelerinizden anında sonuç alın."
    },
    "buttons": {
      "continue": "Devam Et",
      "getStarted": "Başla",
      "skip": "Atla"
    }
  },
  errors: {
    invalidEmail: "Lütfen geçerli bir e-posta adresi girin",
    weakPassword: "Parola çok zayıf. En az 8 karakter uzunluğunda olmalı ve büyük harf, küçük harf, rakam ve özel karakter içermelidir.",
    passwordTooShort: "Parola en az 8 karakter uzunluğunda olmalıdır",
    serverError: "Sunucuda bir hata oluştu. Lütfen daha sonra tekrar deneyin.",
    requiredField: "Bu alan zorunludur",
    requiredFields: "Lütfen tüm zorunlu alanları doldurun",
    updateProfileFailed: "Profil güncellenemedi. Lütfen tekrar deneyin.",
  },
  loginScreen: {
    title: "fiş kolay",
    emailLabel: "E-posta",
    emailPlaceholder: "E-posta adresinizi girin",
    passwordLabel: "Parola",
    passwordPlaceholder: "Parolanızı girin",
    loginButton: "Giriş",
    appleLogin: "Apple ile devam et",
    googleLogin: "Google ile devam et",
    registerLink: "Kayıt ol",
    forgotPassword: "Şifremi unuttum",
    loginErrorTitle: "Giriş başarısız.",
    loginError: "Lütfen bilgilerinizi kontrol edin.",
    invalidCredentials: "Geçersiz e-posta veya şifre.",
    tooManyAttempts: "Çok fazla giriş denemesi. Lütfen daha sonra tekrar deneyin.",
    emptyFieldsError: "Lütfen tüm alanları doldurun.",
    logIn: "Giriş Yap",
    enterDetails:
      "Gizli bilgilerin kilidini açmak için bilgilerinizi girin. Sizi neyin beklediğini asla tahmin edemezsiniz. Ya da belki edersiniz; bu roket bilimi değil.",
    emailFieldLabel: "E-posta",
    passwordFieldLabel: "Şifre",
    emailFieldPlaceholder: "E-posta adresinizi girin",
    passwordFieldPlaceholder: "Süper gizli şifrenizi girin",
    tapToLogIn: "Giriş yapmak için dokunun!",
    hint: "İpucu: herhangi bir e-posta adresi ve favori şifrenizi kullanabilirsiniz :)",
  },
  registerScreen: {
    title: "Kayıt Ol",
    emailLabel: "E-posta",
    emailPlaceholder: "E-posta adresinizi girin",
    passwordLabel: "Parola",
    passwordPlaceholder: "Parolanızı girin",
    passwordMatchError: "Parolalar eşleşmiyor.",
    confirmPasswordLabel: "Parolayı Tekrar Girin",
    confirmPasswordPlaceholder: "Parolanızı tekrar girin",
    createAccount: "Hesap oluştur",
    haveAccount: "Hesabınız var mı?",
    login: "Giriş yap",
    forgotPassword: "Şifremi unuttum",
    successTitle: "Kayıt Başarılı",
    successMessage: "Hesabınız oluşturuldu. Lütfen e-postanızı doğrulayın.",
    registrationError: "Kayıt başarısız. Lütfen tekrar deneyin.",
    emailExists: "Bu e-posta adresi zaten kayıtlı.",
  },
  forgotPasswordScreen: {
    title: "Şifremi Unuttum",
    description: "E-posta adresinizi girin, size şifre sıfırlama kodu göndereceğiz.",
    emailLabel: "E-posta",
    emailPlaceholder: "E-posta adresinizi girin",
    submit: "Kodu Gönder",
    backToLogin: "Girişe dön",
    successTitle: "Kod Gönderildi",
    successMessage: "Lütfen e-postanızı kontrol edin ve sıfırlama kodunu girin.",
  },
  resetPasswordScreen: {
    title: "Şifre Sıfırlama",
    description: "Size gönderilen kodu girin ve yeni şifrenizi belirleyin.",
    codeLabel: "Kod",
    codePlaceholder: "Kodu girin",
    passwordLabel: "Yeni Parola",
    passwordPlaceholder: "Yeni parolanızı girin",
    confirmPasswordLabel: "Parolayı Tekrar Girin",
    confirmPasswordPlaceholder: "Yeni parolanızı tekrar girin",
    submit: "Şifreyi Sıfırla",
    resendCode: "Kodu Tekrar Gönder",
    termsText: "Kaydolarak",
    termsLink: "Kullanım Koşullarını",
    andText: "ve",
    privacyLink: "Gizlilik Politikasını",
  },
  emailVerificationScreen: {
    title: "E-posta Doğrulama",
    description: "Lütfen e-postanıza gönderilen doğrulama kodunu girin",
    codeLabel: "Doğrulama Kodu",
    codePlaceholder: "Doğrulama kodunuzu girin",
    verify: "E-postayı Doğrula",
    resendCode: "Kodu Tekrar Gönder",
    successTitle: "E-posta Doğrulandı",
    successMessage: "E-postanız başarıyla doğrulandı. Şimdi giriş yapabilirsiniz.",
  },
  successScreen: {
    emailVerificationTitle: "E-posta Doğrulandı",
    emailVerificationDescription: "E-posta adresiniz başarıyla doğrulandı.",
    passwordResetTitle: "Şifre Sıfırlandı",
    passwordResetDescription: "Şifreniz başarıyla sıfırlandı.",
    continue: "Devam Et",
    passwordReset: {
      title: "Başarılı",
      message: "Şifreniz değiştirildi, giriş sayfasına yönlendiriliyorsunuz..."
    }
  },
  errorScreen: {
    emailVerificationTitle: "Doğrulama Hatası",
    emailVerificationDescription: "Lütfen geçerli bir doğrulama kodu girin.",
    passwordResetTitle: "Sıfırlama Hatası",
    passwordResetDescription: "Şifre sıfırlama işlemi başarısız oldu.",
    generalTitle: "Hata",
    generalDescription: "Bir hata oluştu. Lütfen tekrar deneyin.",
    tryAgain: "Tekrar Dene",
    codeUsedError: "Bu doğrulama kodu daha önce kullanılmış.",
    codeExpiredError: "Bu doğrulama kodunun süresi dolmuş.",
    invalidCodeError: "Geçersiz doğrulama kodu. Lütfen tekrar deneyin.",
  },
  demoNavigator: {
    componentsTab: "Bileşenler",
    debugTab: "Hata Ayıklama",
    communityTab: "Topluluk",
    podcastListTab: "Podcast",
  },
  demoCommunityScreen: {
    title: "Toplulukla bağlantı kurun",
    tagLine:
      "Infinite Red'in React Native mühendisleri topluluğuna katılın ve uygulama geliştirmenizi bizimle birlikte geliştirin!",
    joinUsOnSlackTitle: "Slack'te bize katılın",
    joinUsOnSlack:
      "Dünya çapındaki React Native mühendisleriyle bağlantı kurabileceğiniz bir yer olsun ister misiniz? Infinite Red Topluluğu Slack'inde sohbete katılın! Büyüyen topluluğumuz, soru sormak, başkalarından öğrenmek ve ağınızı genişletmek için güvenli bir alandır.",
    joinSlackLink: "Slack Topluluğuna Katılın",
    makeIgniteEvenBetterTitle: "Ignite'ı daha da iyi yapın",
    makeIgniteEvenBetter:
      "Ignite'ı daha da iyi yapacak bir fikriniz mi var? Bunu duymaktan mutluluk duyarız! Her zaman en iyi React Native araçlarını oluşturmamıza yardımcı olmak isteyen başkalarını arıyoruz. Ignite'ın geleceğini inşa etmekte bize katılmak için GitHub'da bize katılın.",
    contributeToIgniteLink: "Ignite'a Katkıda Bulunun",
    theLatestInReactNativeTitle: "React Native'de son gelişmeler",
    theLatestInReactNative: "React Native'in sunduğu her şey hakkında sizi güncel tutmak için buradayız.",
    reactNativeRadioLink: "React Native Radio",
    reactNativeNewsletterLink: "React Native Bülteni",
    reactNativeLiveLink: "React Native Live",
    chainReactConferenceLink: "Chain React Konferansı",
    hireUsTitle: "Bir sonraki projeniz için Infinite Red'i işe alın",
    hireUs:
      "Tam bir proje yürütmek veya ekipleri pratik eğitimimizle hızlandırmak olsun, Infinite Red neredeyse her React Native projesiyle yardımcı olabilir.",
    hireUsLink: "Bize mesaj gönderin",
  },
  demoShowroomScreen: {
    jumpStart: "Projenizi hızlandıracak bileşenler!",
    lorem2Sentences:
      "Nulla cupidatat deserunt amet quis aliquip nostrud do adipisicing. Adipisicing excepteur elit laborum Lorem adipisicing do duis.",
    demoHeaderTxExample: "Yaşasın",
    demoViaTxProp: "`tx` Özelliği Üzerinden",
    demoViaSpecifiedTxProp: "`{{prop}}Tx` Özelliği Üzerinden",
  },
  demoDebugScreen: {
    howTo: "NASIL YAPILIR",
    title: "Hata Ayıklama",
    tagLine:
      "Tebrikler, çok gelişmiş bir React Native uygulama şablonuna sahipsiniz. Bu başlangıç şablonundan yararlanın!",
    reactotron: "Reactotron'a Gönder",
    reportBugs: "Hataları Bildir",
    demoList: "Demo Listesi",
    demoPodcastList: "Demo Podcast Listesi",
    androidReactotronHint:
      "Bu çalışmazsa, Reactotron masaüstü uygulamasının çalıştığından emin olun, terminalinizden adb reverse tcp:9090 tcp:9090 komutunu çalıştırın ve uygulamayı yeniden yükleyin.",
    iosReactotronHint:
      "Bu çalışmazsa, Reactotron masaüstü uygulamasının çalıştığından emin olun ve uygulamayı yeniden yükleyin.",
    macosReactotronHint:
      "Bu çalışmazsa, Reactotron masaüstü uygulamasının çalıştığından emin olun ve uygulamayı yeniden yükleyin.",
    webReactotronHint:
      "Bu çalışmazsa, Reactotron masaüstü uygulamasının çalıştığından emin olun ve uygulamayı yeniden yükleyin.",
    windowsReactotronHint:
      "Bu çalışmazsa, Reactotron masaüstü uygulamasının çalıştığından emin olun ve uygulamayı yeniden yükleyin.",
  },
  demoPodcastListScreen: {
    title: "React Native Radio bölümleri",
    onlyFavorites: "Sadece Favorileri Göster",
    favoriteButton: "Favorile",
    unfavoriteButton: "Favoriden Çıkar",
    accessibility: {
      cardHint:
        "Bölümü dinlemek için çift dokunun. Bu bölümü {{action}} için çift dokunun ve basılı tutun.",
      switch: "Sadece favorileri göstermek için açın",
      favoriteAction: "Favori Durumunu Değiştir",
      favoriteIcon: "Bölüm favorilerde değil",
      unfavoriteIcon: "Bölüm favorilerde",
      publishLabel: "{{date}} tarihinde yayınlandı",
      durationLabel: "Süre: {{hours}} saat {{minutes}} dakika {{seconds}} saniye",
    },
    noFavoritesEmptyState: {
      heading: "Bu biraz boş görünüyor",
      content:
        "Henüz favori eklenmedi. Bir bölümü favorilerinize eklemek için kalp simgesine dokunun!",
    },
  },
  profileScreen: {
    emailLabel: "E-posta",
    emailPlaceholder: "E-posta adresinizi girin",
    firstNameLabel: "İsim",
    firstNamePlaceholder: "İsminizi girin",
    lastNameLabel: "Soyisim",
    lastNamePlaceholder: "Soyisminizi girin",
    phoneLabel: "Telefon",
    phonePlaceholder: "Telefon numaranızı girin",
    editTitle: "Profili Düzenle",
  },
  choosePhotoScreen: {
    title: "Profil Fotoğrafını Güncelle",
    takePhoto: "Fotoğraf Çek",
    chooseFromGallery: "Galeriden Seç",
  },
  legal: {
    doc1Title: "Gizlilik Politikası",
    doc1Content: `Gizlilik Politikası

1. Giriş
Biz, Wodo Teknoloji A.Ş. (Wodo) olarak, oyunlar tasarlayan ve geliştiren bir teknoloji şirketi olarak, üçüncü taraf geliştirme şirketlerinin sistemimize entegre olmalarına ve platformumuzda yayınlamalarına yardımcı oluyoruz. Bu Gizlilik Politikası ile çevrimiçi ve çevrimdışı bilgi uygulamalarımızı, toplayabileceğimiz bilgi türlerini, bu bilgileri nasıl kullanmayı, işlemeyi, depolamayı, korumayı, açıklamayı ve paylaşmayı amaçladığımızı ve bir kullanımdan nasıl vazgeçebileceğinizi veya bu tür bilgileri nasıl düzeltebileceğinizi veya değiştirebileceğinizi açıklamayı amaçlıyoruz.

Bu Gizlilik Politikası, Wodo'nun web sitelerine, mobil uygulamalarına, forumlarına ve her türlü bloglarına ve Wodo tarafından sağlanan diğer çevrimiçi veya çevrimdışı hizmetlere uygulanır. Ayrıca, tüm platformlardaki pazarlama ve reklam faaliyetlerimize ve zaman zaman size sağlayabileceğimiz diğer hizmetlere de uygulanır. Bu Gizlilik Politikasında oyunlarımızdan, web sitelerimizden ve diğer hizmetlerimizden toplu olarak "Hizmetlerimiz" olarak bahsediyoruz.

Web sitemizi kullanarak, oyunlarımıza erişerek ve/veya oynayarak veya platformumuz veya diğer Hizmetlerle etkileşime girerek; veya bir şekilde Wodo ile iletişim kurarak, Wodo mevcut teknik olanaklar dahilinde bilgi ve işlemin doğası gereği sizinle ilgili bilgileri toplayabilir. Kişisel bilgilerinizin ve verilerinizin Wodo tarafından ve/veya Wodo aracılığıyla bu Gizlilik Politikasında belirtilen format ve amaçlarla sınırlı olacak şekilde sınırlı bir şekilde kullanılabileceğini kabul ediyor, onaylıyor ve taahhüt ediyorsunuz. Bize veri sağlama veya bu verilerin bu Gizlilik Politikasında açıklandığı şekilde kullanılması konusunda başka endişeleriniz varsa, oyunlarımızı veya diğer Hizmetlerimizi kullanmamalısınız.

Tanımlanmış veya tanımlanabilir gerçek kişiyle ilgili her türlü bilgi kişisel veri teşkil eder. Kişisel verilerinizin işlenmesi, veriler üzerinde gerçekleştirilen elde etme, kaydetme, depolama, muhafaza etme, değiştirme, yeniden düzenleme, açıklama, aktarma, devralma, erişilebilir kılma, hizalama ve erişimi engelleme gibi her türlü işlemi ifade eder.

Lütfen Wodo'nun Platformların (aşağıda tanımlanmıştır) eylemlerinden, sitelerinin içeriğinden, onlara sağladığınız bilgilerin kullanımından veya sunabilecekleri herhangi bir hizmetten sorumlu olmadığını unutmayın.

Wodo bu Gizlilik Politikasını değiştirme hakkını saklı tutar ve Hizmetleri kullanmaya devam etmeniz, Gizlilik Politikasındaki değişiklikleri kabul ettiğiniz anlamına gelecektir.

2. Veri Sorumlusunun Kimliği
Veri sorumlusu Wodo Teknoloji A.Ş.'dir.

3. Kişisel Verilerinizi Nasıl Topluyoruz
Hizmetlerimizi cihazınıza indirdiğinizde veya kullandığınızda, bize geri bildirimde bulunduğunuzda veya bilgilerinizi veya destek taleplerinizi doğrudan Wodo'ya, Apple App Store ve Google Play App Store (birlikte "Platformlar" olarak anılacaktır) platformlarına ve rızanızı almış veya sizinle ilgili bu tür kişisel bilgileri bizimle paylaşmak için başka bir yasal hakka sahip olan iş ortaklarına veya diğer üçüncü taraf şirketlere (reklam platformları ve ortakları ile veri toplayıcıları dahil) ilettiğinizde otomatik olarak kişisel veri toplayabiliriz. Bu, sizinle ve ilgi alanlarınızla ilgili özellikler ile kullandığınız diğer oyunlar ve hizmetler, demografik ve genel konum bilgileri gibi bilgileri içerebilir. Kişisel verilerinizi bu Gizlilik Politikasında açıklandığı şekilde kullanacağız.`,
    doc2Title: "Kullanım Koşulları",
    doc2Content: `Kullanım Koşulları

1. Giriş
Biz, Wodo Teknoloji A.Ş. (Wodo) olarak, oyunlar tasarlayan ve geliştiren bir teknoloji şirketi olarak, üçüncü taraf geliştirme şirketlerinin sistemimize entegre olmalarına ve platformumuzda yayınlamalarına yardımcı oluyoruz. Bu Gizlilik Politikası ile çevrimiçi ve çevrimdışı bilgi uygulamalarımızı, toplayabileceğimiz bilgi türlerini, bu bilgileri nasıl kullanmayı, işlemeyi, depolamayı, korumayı, açıklamayı ve paylaşmayı amaçladığımızı ve bir kullanımdan nasıl vazgeçebileceğinizi veya bu tür bilgileri nasıl düzeltebileceğinizi veya değiştirebileceğinizi açıklamayı amaçlıyoruz.

Bu Gizlilik Politikası, Wodo'nun web sitelerine, mobil uygulamalarına, forumlarına ve her türlü bloglarına ve Wodo tarafından sağlanan diğer çevrimiçi veya çevrimdışı hizmetlere uygulanır. Ayrıca, tüm platformlardaki pazarlama ve reklam faaliyetlerimize ve zaman zaman size sağlayabileceğimiz diğer hizmetlere de uygulanır. Bu Gizlilik Politikasında oyunlarımızdan, web sitelerimizden ve diğer hizmetlerimizden toplu olarak "Hizmetlerimiz" olarak bahsediyoruz.

Web sitemizi kullanarak, oyunlarımıza erişerek ve/veya oynayarak veya platformumuz veya diğer Hizmetlerle etkileşime girerek; veya bir şekilde Wodo ile iletişim kurarak, Wodo mevcut teknik olanaklar dahilinde bilgi ve işlemin doğası gereği sizinle ilgili bilgileri toplayabilir. Kişisel bilgilerinizin ve verilerinizin Wodo tarafından ve/veya Wodo aracılığıyla bu Gizlilik Politikasında belirtilen format ve amaçlarla sınırlı olacak şekilde sınırlı bir şekilde kullanılabileceğini kabul ediyor, onaylıyor ve taahhüt ediyorsunuz. Bize veri sağlama veya bu verilerin bu Gizlilik Politikasında açıklandığı şekilde kullanılması konusunda başka endişeleriniz varsa, oyunlarımızı veya diğer Hizmetlerimizi kullanmamalısınız.

Tanımlanmış veya tanımlanabilir gerçek kişiyle ilgili her türlü bilgi kişisel veri teşkil eder. Kişisel verilerinizin işlenmesi, veriler üzerinde gerçekleştirilen elde etme, kaydetme, depolama, muhafaza etme, değiştirme, yeniden düzenleme, açıklama, aktarma, devralma, erişilebilir kılma, hizalama ve erişimi engelleme gibi her türlü işlemi ifade eder.

Lütfen Wodo'nun Platformların (aşağıda tanımlanmıştır) eylemlerinden, sitelerinin içeriğinden, onlara sağladığınız bilgilerin kullanımından veya sunabilecekleri herhangi bir hizmetten sorumlu olmadığını unutmayın.

Wodo bu Gizlilik Politikasını değiştirme hakkını saklı tutar ve Hizmetleri kullanmaya devam etmeniz, Gizlilik Politikasındaki değişiklikleri kabul ettiğiniz anlamına gelecektir.

2. Veri Sorumlusunun Kimliği
Veri sorumlusu Wodo Teknoloji A.Ş.'dir.

3. Kişisel Verilerinizi Nasıl Topluyoruz
Hizmetlerimizi cihazınıza indirdiğinizde veya kullandığınızda, bize geri bildirimde bulunduğunuzda veya bilgilerinizi veya destek taleplerinizi doğrudan Wodo'ya, Apple App Store ve Google Play App Store (birlikte "Platformlar" olarak anılacaktır) platformlarına ve rızanızı almış veya sizinle ilgili bu tür kişisel bilgileri bizimle paylaşmak için başka bir yasal hakka sahip olan iş ortaklarına veya diğer üçüncü taraf şirketlere (reklam platformları ve ortakları ile veri toplayıcıları dahil) ilettiğinizde otomatik olarak kişisel veri toplayabiliriz. Bu, sizinle ve ilgi alanlarınızla ilgili özellikler ile kullandığınız diğer oyunlar ve hizmetler, demografik ve genel konum bilgileri gibi bilgileri içerebilir. Kişisel verilerinizi bu Gizlilik Politikasında açıklandığı şekilde kullanacağız.`,
  },
  registrationOptions: {
    title: "Ben Bir",
    accountant: "Muhasebeci/mali müşavirim",
    taxpayer: "Mükellefim",
    subtitle: "Kolay muhasebe",
  },
}

export default tr
export type Translations = typeof tr 