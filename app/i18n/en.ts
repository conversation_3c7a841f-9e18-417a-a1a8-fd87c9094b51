const en = {
  common: {
    ok: "OK",
    cancel: "Cancel",
    back: "Back",
    backWithArrow: "< Back",
    error: "Error",
    logOut: "Log Out",
    close: "Close",
    save: "Save",
    edit: "Edit",
  },
  welcomeScreen: {
    postscript:
      "psst  — This probably isn't what your app looks like. (Unless your designer handed you these screens, and in that case, ship it!)",
    readyForLaunch: "Your app, almost ready for launch!",
    exciting: "(ohh, this is exciting!)",
    letsGo: "Let's go!",
  },
  errorScreen: {
    title: "Something went wrong!",
    friendlySubtitle:
      "This is the screen that your users will see in production when an error is thrown. You'll want to customize this message (located in `app/i18n/en.ts`) and probably the layout as well (`app/screens/ErrorScreen`). If you want to remove this entirely, check `app/app.tsx` for the <ErrorBoundary> component.",
    reset: "RESET APP",
    traceTitle: "Error from %{name} stack",
    emailVerificationTitle: "Verification Error",
    emailVerificationDescription: "Please enter a valid verification code.",
    passwordResetTitle: "Reset Error",
    passwordResetDescription: "Password reset failed.",
    generalTitle: "Error",
    generalDescription: "An error occurred. Please try again.",
    tryAgain: "Try Again",
    codeUsedError: "This verification code has already been used.",
    codeExpiredError: "This verification code has expired.",
    invalidCodeError: "Invalid verification code. Please try again.",
  },
  emptyStateComponent: {
    generic: {
      heading: "So empty... so sad",
      content: "No data found yet. Try clicking the button to refresh or reload the app.",
      button: "Let's try this again",
    },
  },
  "onboarding": {
    "uploadPhotos": {
      "title": "Upload Photos",
      "description": "Upload your photos to the system."
    },
    "processImages": {
      "title": "Process Images",
      "description": "We'll process your images automatically."
    },
    "viewResults": {
      "title": "View Results",
      "description": "Get instant results from your uploads."
    },
    "buttons": {
      "continue": "Continue",
      "getStarted": "Get Started",
      "skip": "Skip"
    }
  },
  errors: {
    invalidEmail: "Please enter a valid email address",
    weakPassword: "Password is too weak. It should contain at least 8 characters, including uppercase, lowercase, numbers, and special characters",
    passwordTooShort: "Password must be at least 8 characters",
    serverError: "An error occurred on the server. Please try again later.",
    requiredField: "This field is required",
    requiredFields: "Please fill in all required fields",
    updateProfileFailed: "Failed to update profile. Please try again.",
  },
  loginScreen: {
    title: "fiş kolay",
    emailLabel: "Email",
    emailPlaceholder: "Enter your email",
    passwordLabel: "Password",
    passwordPlaceholder: "Enter your password",
    loginButton: "Login",
    appleLogin: "Continue with Apple",
    googleLogin: "Continue with Google",
    registerLink: "Register",
    forgotPassword: "Forgot Password",
    loginErrorTitle: "Login failed.",
    loginError: "Please check your credentials.",
    invalidCredentials: "Invalid email or password.",
    tooManyAttempts: "Too many login attempts. Please try again later.",
    emptyFieldsError: "Please fill in all fields.",
    logIn: "Log In",
    enterDetails:
      "Enter your details below to unlock top secret info. You'll never guess what we've got waiting. Or maybe you will; it's not rocket science here.",
    emailFieldLabel: "Email",
    passwordFieldLabel: "Password",
    emailFieldPlaceholder: "Enter your email address",
    passwordFieldPlaceholder: "Super secret password here",
    tapToLogIn: "Tap to log in!",
    hint: "Hint: you can use any email address and your favorite password :)",
  },
  registerScreen: {
    title: "Register",
    emailLabel: "Email",
    emailPlaceholder: "Enter your email",
    passwordLabel: "Password",
    passwordPlaceholder: "Enter your password",
    confirmPasswordLabel: "Confirm Password",
    confirmPasswordPlaceholder: "Confirm your password",
    createAccount: "Create account",
    haveAccount: "Already have an account?",
    login: "Login",
    forgotPassword: "Forgot Password",
    passwordMatchError: "Passwords do not match.",
    successTitle: "Registration Successful",
    successMessage: "Your account has been created. Please verify your email.",
    registrationError: "Registration failed. Please try again.",
    emailExists: "This email is already registered.",
  },
  forgotPasswordScreen: {
    title: "Forgot Password",
    description: "Enter your email address and we'll send you a code to reset your password.",
    emailLabel: "Email",
    emailPlaceholder: "Enter your email",
    submit: "Send Reset Code",
    backToLogin: "Back to Login",
    successTitle: "Code Sent",
    successMessage: "Please check your email for the password reset code.",
    emailRequired: "Please enter your email address.",
    tooManyAttempts: "Too many attempts. Please try again later.",
    generalError: "Failed to send reset code. Please try again.",
    resendCode: "Resend Code",
    codeSent: "A new code has been sent to your email.",
  },
  resetPasswordScreen: {
    title: "Reset Password",
    description: "Enter the code we sent you and set your new password.",
    codeLabel: "Code",
    codePlaceholder: "Enter code",
    passwordLabel: "New Password",
    passwordPlaceholder: "Enter new password",
    confirmPasswordLabel: "Confirm Password",
    confirmPasswordPlaceholder: "Confirm new password",
    submit: "Reset Password",
    resendCode: "Resend Code",
    termsText: "By Signing Up You're Agreeing To The",
    termsLink: "Terms & Conditions",
    andText: "and",
    privacyLink: "Privacy Policy",
    allFieldsRequired: "Please fill in all fields",
    passwordsDoNotMatch: "Passwords do not match",
    resetError: "Failed to reset password. Please try again.",
  },
  emailVerificationScreen: {
    title: "Email Verification",
    description: "Please enter the verification code sent to your email",
    codeLabel: "Verification Code",
    codePlaceholder: "Enter your verification code",
    verify: "Verify Email",
    resendCode: "Resend Code",
    successTitle: "Email Verified",
    successMessage: "Your email has been successfully verified. You can now login.",
  },
  successScreen: {
    emailVerificationTitle: "Email Verified",
    emailVerificationDescription: "Your email has been successfully verified.",
    passwordResetTitle: "Password Reset",
    passwordResetDescription: "Your password has been successfully reset.",
    continue: "Continue",
    passwordReset: {
      title: "Success",
      message: "Your password is now changed, redirecting to login..."
    }
  },
  demoNavigator: {
    componentsTab: "Components",
    debugTab: "Debug",
    communityTab: "Community",
    podcastListTab: "Podcast",
  },
  demoCommunityScreen: {
    title: "Connect with the community",
    tagLine:
      "Plug in to Infinite Red's community of React Native engineers and level up your app development with us!",
    joinUsOnSlackTitle: "Join us on Slack",
    joinUsOnSlack:
      "Wish there was a place to connect with React Native engineers around the world? Join the conversation in the Infinite Red Community Slack! Our growing community is a safe space to ask questions, learn from others, and grow your network.",
    joinSlackLink: "Join the Slack Community",
    makeIgniteEvenBetterTitle: "Make Ignite even better",
    makeIgniteEvenBetter:
      "Have an idea to make Ignite even better? We're happy to hear that! We're always looking for others who want to help us build the best React Native tooling out there. Join us over on GitHub to join us in building the future of Ignite.",
    contributeToIgniteLink: "Contribute to Ignite",
    theLatestInReactNativeTitle: "The latest in React Native",
    theLatestInReactNative: "We're here to keep you current on all React Native has to offer.",
    reactNativeRadioLink: "React Native Radio",
    reactNativeNewsletterLink: "React Native Newsletter",
    reactNativeLiveLink: "React Native Live",
    chainReactConferenceLink: "Chain React Conference",
    hireUsTitle: "Hire Infinite Red for your next project",
    hireUs:
      "Whether it's running a full project or getting teams up to speed with our hands-on training, Infinite Red can help with just about any React Native project.",
    hireUsLink: "Send us a message",
  },
  demoShowroomScreen: {
    jumpStart: "Components to jump start your project!",
    lorem2Sentences:
      "Nulla cupidatat deserunt amet quis aliquip nostrud do adipisicing. Adipisicing excepteur elit laborum Lorem adipisicing do duis.",
    demoHeaderTxExample: "Yay",
    demoViaTxProp: "Via `tx` Prop",
    demoViaSpecifiedTxProp: "Via `{{prop}}Tx` Prop",
  },
  demoDebugScreen: {
    howTo: "HOW TO",
    title: "Debug",
    tagLine:
      "Congratulations, you've got a very advanced React Native app template here.  Take advantage of this boilerplate!",
    reactotron: "Send to Reactotron",
    reportBugs: "Report Bugs",
    demoList: "Demo List",
    demoPodcastList: "Demo Podcast List",
    androidReactotronHint:
      "If this doesn't work, ensure the Reactotron desktop app is running, run adb reverse tcp:9090 tcp:9090 from your terminal, and reload the app.",
    iosReactotronHint:
      "If this doesn't work, ensure the Reactotron desktop app is running and reload app.",
    macosReactotronHint:
      "If this doesn't work, ensure the Reactotron desktop app is running and reload app.",
    webReactotronHint:
      "If this doesn't work, ensure the Reactotron desktop app is running and reload app.",
    windowsReactotronHint:
      "If this doesn't work, ensure the Reactotron desktop app is running and reload app.",
  },
  demoPodcastListScreen: {
    title: "React Native Radio episodes",
    onlyFavorites: "Only Show Favorites",
    favoriteButton: "Favorite",
    unfavoriteButton: "Unfavorite",
    accessibility: {
      cardHint:
        "Double tap to listen to the episode. Double tap and hold to {{action}} this episode.",
      switch: "Switch on to only show favorites",
      favoriteAction: "Toggle Favorite",
      favoriteIcon: "Episode not favorited",
      unfavoriteIcon: "Episode favorited",
      publishLabel: "Published {{date}}",
      durationLabel: "Duration: {{hours}} hours {{minutes}} minutes {{seconds}} seconds",
    },
    noFavoritesEmptyState: {
      heading: "This looks a bit empty",
      content:
        "No favorites have been added yet. Tap the heart on an episode to add it to your favorites!",
    },
  },
  legal: {
    doc1Title: "Privacy Policy",
    doc1Content: `Privacy Policy

1. Introduction
We, as Wodo Teknoloji A.S. (Wodo), which is a technology company that designs and develops games, help third-party development companies to integrate to our system and publish them on our platform. With this Privacy Policy, we intend to explain our online and offline information practices, the kinds of information we may collect, how we intend to use, process, store, protect, disclose, and share that information, and how you can opt-out of a use or correct or change such information.

This Privacy Policy applies to websites, mobile applications, forums, and every kind of blogs of Wodo and any other online or offline services provided by Wodo. It also applies to our marketing and advertising activities on all platforms and other services that we may provide to you from time to time. In this Privacy Policy we refer to our games and websites and other services collectively as our "Services".

By using our website, accessing and/or playing our games, or interacting with our platform or other Services; or somehow communicating with Wodo, Wodo may collect information about you in accordance with the nature of the information and transaction within the current technical possibilities. You accept, acknowledge, and promise that your personal information and data might be used in limited manner by Wodo and/or through Wodo to the extent that it is limited to the format and purposes set forth in this Privacy Policy. If you have any other concerns about providing data to us or it being used as described in this Privacy Policy, you should not use our games or other Services.

Every type of information related to an identified or identifiable natural person constitutes personal data. Processing your personal data signifies all types of transactions performed on the data such as obtaining, recording, storing, preserving, altering, re-arranging, disclosing, transferring, acquiring, making accessible, alignment and preventing access.

Please note that Wodo is not responsible for the actions of Platforms (defined below), the content of their sites, the use of information you provide to them or any services they may offer.

Wodo reserves the right to modify this Privacy Policy and your continued use of the Services shall mean your acceptance of the changes to this Privacy Policy.

2. The Identity of the Data Controller
The data controller is Wodo Teknoloji A.S.

3. How We Collect Your Personal Data
We may collect personal data automatically whenever you download or use or Services to your device, whenever you send us your feedback or convey your information or support requests directly to Wodo, Apple App Store and Google Play App Store (Referred to as "Platforms" together) platforms and the business partners or other third-party companies who have obtained your consent or have another legal right to share such personal with us (including advertising platforms and partners and data aggregators who have obtained). This may include attributes about you and your interests, as well as other games and services you use, demographic and general location information. We will use your personal data as described in this Privacy Policy.`,
    doc2Title: "Terms & Conditions",
    doc2Content: `Terms & Conditions

1. Introduction
We, as Wodo Teknoloji A.S. (Wodo), which is a technology company that designs and develops games, help third-party development companies to integrate to our system and publish them on our platform. With this Privacy Policy, we intend to explain our online and offline information practices, the kinds of information we may collect, how we intend to use, process, store, protect, disclose, and share that information, and how you can opt-out of a use or correct or change such information.

This Privacy Policy applies to websites, mobile applications, forums, and every kind of blogs of Wodo and any other online or offline services provided by Wodo. It also applies to our marketing and advertising activities on all platforms and other services that we may provide to you from time to time. In this Privacy Policy we refer to our games and websites and other services collectively as our "Services".

By using our website, accessing and/or playing our games, or interacting with our platform or other Services; or somehow communicating with Wodo, Wodo may collect information about you in accordance with the nature of the information and transaction within the current technical possibilities. You accept, acknowledge, and promise that your personal information and data might be used in limited manner by Wodo and/or through Wodo to the extent that it is limited to the format and purposes set forth in this Privacy Policy. If you have any other concerns about providing data to us or it being used as described in this Privacy Policy, you should not use our games or other Services.

Every type of information related to an identified or identifiable natural person constitutes personal data. Processing your personal data signifies all types of transactions performed on the data such as obtaining, recording, storing, preserving, altering, re-arranging, disclosing, transferring, acquiring, making accessible, alignment and preventing access.

Please note that Wodo is not responsible for the actions of Platforms (defined below), the content of their sites, the use of information you provide to them or any services they may offer.

Wodo reserves the right to modify this Privacy Policy and your continued use of the Services shall mean your acceptance of the changes to this Privacy Policy.

2. The Identity of the Data Controller
The data controller is Wodo Teknoloji A.S.

3. How We Collect Your Personal Data
We may collect personal data automatically whenever you download or use or Services to your device, whenever you send us your feedback or convey your information or support requests directly to Wodo, Apple App Store and Google Play App Store (Referred to as "Platforms" together) platforms and the business partners or other third-party companies who have obtained your consent or have another legal right to share such personal with us (including advertising platforms and partners and data aggregators who have obtained). This may include attributes about you and your interests, as well as other games and services you use, demographic and general location information. We will use your personal data as described in this Privacy Policy.`,
  },
  profileScreen: {
    emailLabel: "Email",
    emailPlaceholder: "Enter your email",
    firstNameLabel: "First Name",
    firstNamePlaceholder: "Enter your first name",
    lastNameLabel: "Last Name",
    lastNamePlaceholder: "Enter your last name",
    phoneLabel: "Phone",
    phonePlaceholder: "Enter your phone number",
    editTitle: "Edit Profile",
  },
  choosePhotoScreen: {
    title: "Update profile photo",
    takePhoto: "Take Photo",
    chooseFromGallery: "Choose from Gallery",
  },
  registrationOptions: {
    title: "I am a",
    accountant: "Accountant/Financial Advisor",
    taxpayer: "Taxpayer",
    subtitle: "Easy accounting",
  },
}

export default en
export type Translations = typeof en
