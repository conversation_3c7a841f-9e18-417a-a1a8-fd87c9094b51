/**
 * This Api class lets you define an API endpoint and methods to request
 * data and process it.
 *
 * See the [Backend API Integration](https://docs.infinite.red/ignite-cli/boilerplate/app/services/#backend-api-integration)
 * documentation for more details.
 */
import { ApiResponse, ApisauceInstance, create } from "apisauce"
import Config from "../../config"
import { GeneralApiProblem, getGeneralApiProblem } from "./apiProblem"
import type {
  ApiConfig,
  ApiFeedResponse,
  CreateUserRequest,
  User,
  AuthResponse,
  ExtendedProblemCode,
} from "./api.types"
import type { EpisodeSnapshotIn } from "../../models/Episode"
import AsyncStorage from "@react-native-async-storage/async-storage"
import { navigate } from "../../navigators"

const STORAGE_KEYS = {
  AUTH_TOKENS: "auth_tokens",
} as const

const AUTH_HEADERS = {
  AUTHORIZATION: "Authorization",
  USER_GROUPS: "x-user-groups",
} as const

export const API_ERRORS = {
  INVALID_CREDENTIALS: "INVALID_CREDENTIALS",
  TOKEN_EXPIRED: "TOKEN_EXPIRED",
  INVALID_TOKEN: "INVALID_TOKEN",
  USER_NOT_FOUND: "USER_NOT_FOUND",
  EMAIL_ALREADY_EXISTS: "EMAIL_ALREADY_EXISTS",
  INVALID_CODE: "INVALID_CODE",
  TOO_MANY_ATTEMPTS: "TOO_MANY_ATTEMPTS",
  NETWORK_ERROR: "NETWORK_ERROR",
  UNKNOWN_ERROR: "UNKNOWN_ERROR",
} as const

export type ApiError = (typeof API_ERRORS)[keyof typeof API_ERRORS]

const log = (message: string, data?: any) => {
  const timestamp = new Date().toISOString()
  console.log(`[API ${timestamp}] ${message}`, data ? JSON.stringify(data, null, 2) : "")
}

/**
 * Configuring the apisauce instance.
 */
export const DEFAULT_API_CONFIG: ApiConfig = {
  url: Config.API_URL,
  timeout: 10000,
}

/**
 * Manages all requests to the API. You can use this class to build out
 * various requests that you need to call from your backend API.
 */
export class Api {
  apisauce: ApisauceInstance
  config: ApiConfig
  private accessToken: string | null = null
  private refreshTokenValue: string | null = null

  /**
   * Set up our API instance. Keep this lightweight!
   */
  constructor(config: ApiConfig = DEFAULT_API_CONFIG) {
    this.config = config
    log("Initializing API with config:", config)

    this.apisauce = create({
      baseURL: this.config.url,
      timeout: this.config.timeout,
      headers: {
        Accept: "application/json",
      },
    })

    // Load saved tokens if they exist
    this.loadSavedTokens()

    // Add response transform to handle common error cases
    this.apisauce.addResponseTransform((response) => {
      log(`Response received for ${response.config?.url}:`, {
        status: response.status,
        ok: response.ok,
        problem: response.problem,
        data: response.data,
      })

      if (!response.ok) {
        const status = response.status
        const data = response.data as { error?: string }
        log(`Error response for ${response.config?.url}:`, { status, data })

        // Handle unauthorized errors globally
        if (status === 401) {
          // Skip for login and refresh token endpoints to avoid loops
          const skipUrls = ["/users/login", "/auth/refresh-token"]
          if (!skipUrls.some((url) => response.config?.url?.includes(url))) {
            // Clear tokens and redirect to login
            this.clearAuthTokens().then(() => {
              navigate("Login")
            })
          }

          if (data?.error === "token_expired") {
            ;(response as any).customProblem = API_ERRORS.TOKEN_EXPIRED
          } else if (data?.error === "invalid_token") {
            ;(response as any).customProblem = API_ERRORS.INVALID_TOKEN
          } else {
            ;(response as any).customProblem = API_ERRORS.INVALID_CREDENTIALS
          }
        } else if (status === 404) {
          ;(response as any).customProblem = API_ERRORS.USER_NOT_FOUND
        } else if (status === 409) {
          ;(response as any).customProblem = API_ERRORS.EMAIL_ALREADY_EXISTS
        } else if (status === 429) {
          ;(response as any).customProblem = API_ERRORS.TOO_MANY_ATTEMPTS
        } else if (status === 400 && data?.error === "invalid_code") {
          ;(response as any).customProblem = API_ERRORS.INVALID_CODE
        }
        log(`Transformed error for ${response.config?.url}:`, {
          customProblem: (response as any).customProblem,
        })
      }
    })

    // Add request transform to handle token refresh
    let isRefreshing = false
    let failedQueue: { resolve: (token: string) => void; reject: (error: any) => void }[] = []

    const processQueue = (error: any, token: string | null = null) => {
      log("Processing failed queue:", {
        queueLength: failedQueue.length,
        error: error?.message,
        token,
      })
      failedQueue.forEach((prom) => {
        if (error) {
          prom.reject(error)
        } else if (token) {
          prom.resolve(token)
        }
      })
      failedQueue = []
    }

    this.apisauce.addAsyncResponseTransform(async (response) => {
      const { status, data, config } = response
      log("Response intercepted:", { status, data, url: config?.url })

      // Skip token refresh for auth-related endpoints
      const skipUrls = ["/users/login", "/users/register", "/auth/refresh-token"]
      if (skipUrls.some((url) => config?.url?.includes(url))) {
        log("Skipping token refresh for auth endpoint:", config?.url)
        return
      }

      // Only proceed if we get a 401 with token_expired error
      if (status !== 401 || data?.error !== "token_expired") {
        return
      }

      try {
        if (isRefreshing) {
          log("Token refresh in progress, queueing request")
          try {
            const newToken = await new Promise<string>((resolve, reject) => {
              failedQueue.push({ resolve, reject })
            })
            log("New token received from queue")
            // Retry the original request with new token
            const method = config?.method?.toLowerCase() || "get"
            const url = config?.url || "/"
            let retryResponse
            switch (method) {
              case "get":
                retryResponse = await this.apisauce.get(url, config?.data)
                break
              case "post":
                retryResponse = await this.apisauce.post(url, config?.data)
                break
              case "put":
                retryResponse = await this.apisauce.put(url, config?.data)
                break
              case "patch":
                retryResponse = await this.apisauce.patch(url, config?.data)
                break
              case "delete":
                retryResponse = await this.apisauce.delete(url, config?.data)
                break
              default:
                retryResponse = await this.apisauce.get(url, config?.data)
            }
            Object.assign(response, retryResponse)
            return
          } catch (err) {
            log("Error while waiting for token refresh:", err)
            throw err
          }
        }

        isRefreshing = true
        log("Starting token refresh")
        const tokensStr = await AsyncStorage.getItem(STORAGE_KEYS.AUTH_TOKENS)
        const tokens = tokensStr ? JSON.parse(tokensStr) : null

        if (!tokens?.refreshToken) {
          log("No refresh token found in storage")
          processQueue(new Error("No refresh token"))
          throw new Error("No refresh token")
        }

        const result = await this.refreshToken(tokens.refreshToken)
        if (result.kind === "ok") {
          const newAccessToken = result.tokens.accessToken
          log("Token refresh successful")
          // Retry the original request with new token
          const method = config?.method?.toLowerCase() || "get"
          const url = config?.url || "/"
          let retryResponse
          switch (method) {
            case "get":
              retryResponse = await this.apisauce.get(url, config?.data)
              break
            case "post":
              retryResponse = await this.apisauce.post(url, config?.data)
              break
            case "put":
              retryResponse = await this.apisauce.put(url, config?.data)
              break
            case "patch":
              retryResponse = await this.apisauce.patch(url, config?.data)
              break
            case "delete":
              retryResponse = await this.apisauce.delete(url, config?.data)
              break
            default:
              retryResponse = await this.apisauce.get(url, config?.data)
          }
          Object.assign(response, retryResponse)
          processQueue(null, newAccessToken)
        } else {
          log("Token refresh failed:", result)
          processQueue(new Error("Failed to refresh token"))
          throw new Error("Failed to refresh token")
        }
      } catch (error) {
        log("Error in response transform:", error)
        processQueue(error)
        throw error
      } finally {
        isRefreshing = false
      }
    })
  }

  /**
   * Load saved tokens from AsyncStorage and set them in the API client
   */
  private async loadSavedTokens() {
    try {
      const tokensStr = await AsyncStorage.getItem(STORAGE_KEYS.AUTH_TOKENS)
      if (tokensStr) {
        const tokens = JSON.parse(tokensStr)
        if (tokens?.accessToken && tokens?.refreshToken) {
          this.setAuthTokens(tokens.accessToken, tokens.refreshToken)
          log("Loaded tokens from storage:", { accessToken: tokens.accessToken })
        } else {
          log("No valid tokens found in storage:", tokens)
        }
      } else {
        log("No tokens found in AsyncStorage")
      }
    } catch (error) {
      log("Error loading saved tokens:", error)
    }
  }
  /**
   * Gets a list of recent React Native Radio episodes.
   */
  async getEpisodes(): Promise<{ kind: "ok"; episodes: EpisodeSnapshotIn[] } | GeneralApiProblem> {
    // make the api call
    const response: ApiResponse<ApiFeedResponse> = await this.apisauce.get(
      `api.json?rss_url=https%3A%2F%2Ffeeds.simplecast.com%2FhEI_f9Dx`,
    )

    // the typical ways to die when calling an api
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }

    // transform the data into the format we are expecting
    try {
      const rawData = response.data

      // This is where we transform the data into the shape we expect for our MST model.
      const episodes: EpisodeSnapshotIn[] =
        rawData?.items.map((raw) => ({
          ...raw,
        })) ?? []

      return { kind: "ok", episodes }
    } catch (e) {
      if (__DEV__ && e instanceof Error) {
        console.error(`Bad data: ${e.message}\n${response.data}`, e.stack)
      }
      return { kind: "bad-data" }
    }
  }

  /**
   * Registers a new user with the provided information.
   */
  async register(
    userData: CreateUserRequest,
  ): Promise<{ kind: "ok"; user: User } | GeneralApiProblem> {
    log("Registering user:", { email: userData.email })

    const response: ApiResponse<User> = await this.apisauce.post("/users/register", userData)
    log("Register response:", {
      status: response.status,
      ok: response.ok,
      problem: response.problem,
      data: response.data,
    })

    // For successful registration (201 Created)
    if (response.status === 201 && response.data) {
      try {
        // Transform the backend response to match our MST model
        const user: User = {
          id: Number(response.data.id),
          email: response.data.email,
          name: response.data.name || undefined,
          surname: response.data.surname || undefined,
          userType: response.data.userType || "B",
          isApproved: response.data.state === "approved",
          state: response.data.state || "pending",
          createdAt: response.data.createdAt,
          updatedAt: response.data.updatedAt,
          avatar: response.data.avatar || undefined,
        }

        log("Register successful:", { userId: user.id })
        return { kind: "ok", user }
      } catch (e) {
        if (__DEV__ && e instanceof Error) {
          log("Register data error:", { error: e.message, data: response.data })
        }
        return { kind: "bad-data" }
      }
    }

    const problem = getGeneralApiProblem(response)
    if (problem) {
      log("Register failed:", problem)
      return problem
    }

    return { kind: "unknown", temporary: true }
  }

  /**
   * Logs in a user with email and password
   */
  async login(credentials: {
    email: string
    password: string
  }): Promise<{ kind: "ok"; user: User; tokens: AuthResponse } | GeneralApiProblem> {
    log("Logging in user:", { email: credentials.email })

    const response: ApiResponse<{ user: User } & AuthResponse> = await this.apisauce.post(
      "/users/login",
      credentials,
    )
    log("Login response:", { status: response.status, ok: response.ok, problem: response.problem })

    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) {
        log("Login failed:", problem)
        return problem
      }
    }

    try {
      const data = response.data
      if (!data) throw new Error("No data received from the server")
      const { user, accessToken, refreshToken } = data

      if (!user || !accessToken || !refreshToken) throw new Error("Invalid response data")

      log("Login successful, setting tokens:", { userId: user.id, accessToken })
      this.setAuthTokens(accessToken, refreshToken) // This should set this.accessToken
      return { kind: "ok", user, tokens: { accessToken, refreshToken } }
    } catch (e) {
      if (__DEV__ && e instanceof Error) {
        log("Login data error:", { error: e.message, data: response.data })
      }
      return { kind: "bad-data" }
    }
  }
  /**
   * Approves a user with verification code
   */
  async approveUser(code: string): Promise<{ kind: "ok"; user: User } | GeneralApiProblem> {
    const response: ApiResponse<User> = await this.apisauce.post(`/users/approve/${code}`)

    log("Approve user response:", {
      ok: response.ok,
      problem: response.problem,
      data: response.data,
    })

    if (response.ok && response.data) {
      try {
        // Transform the backend response to match our MST model
        const user: User = {
          id: Number(response.data.id),
          email: response.data.email,
          name: response.data.name || undefined,
          surname: response.data.surname || undefined,
          userType: response.data.userType || "B",
          isApproved: response.data.state === "approved",
          state: response.data.state || "pending",
          createdAt: response.data.createdAt,
          updatedAt: response.data.updatedAt,
          avatar: response.data.avatar || undefined,
        }

        log("Approve user successful:", { userId: user.id })
        return { kind: "ok", user }
      } catch (e) {
        if (__DEV__ && e instanceof Error) {
          log("Approve user data error:", { error: e.message, data: response.data })
        }
        return { kind: "bad-data" }
      }
    }

    const problem = getGeneralApiProblem(response)
    if (problem) {
      let apir = {
        ...problem,
        error:
          (response.data as any)?.details || (response.data as any)?.message || "Unknown error",
      }
      log("Approve user failed:", apir)
      // Include the error details in the response
      return apir
    }

    return { kind: "unknown", temporary: true }
  }

  /**
   * Updates user profile
   */
  async updateProfile(userData: {
    name?: string
    surname?: string
  }): Promise<{ kind: "ok"; user: User } | GeneralApiProblem> {
    log("Updating user profile:", userData)

    const response: ApiResponse<User> = await this.apisauce.patch("/users/profile", userData)
    log("Update profile response:", {
      status: response.status,
      ok: response.ok,
      problem: response.problem,
    })

    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) {
        log("Update profile failed:", problem)
        return problem
      }
    }

    try {
      const user = response.data
      if (!user) throw new Error("No data received from the server")

      log("Update profile successful:", { userId: user.id })
      return { kind: "ok", user }
    } catch (e) {
      if (__DEV__ && e instanceof Error) {
        log("Update profile data error:", { error: e.message, data: response.data })
      }
      return { kind: "bad-data" }
    }
  }

  /**
   * Updates user profile photo
   */
  async updateProfilePhoto(
    photoUri: string,
    accessToken?: string,
  ): Promise<{ kind: "ok"; avatar: string } | GeneralApiProblem> {
    log("Updating profile photo")
    const token = accessToken || this.accessToken
    log("Using accessToken:", token)
    log("Current apisauce headers before request:", this.apisauce.headers)

    if (!token) {
      log("No access token available, cannot update photo")
      return { kind: "unauthorized", error: "No access token available" }
    }

    const formData = new FormData()
    formData.append("photo", {
      uri: photoUri,
      type: "image/jpeg",
      name: "photo.jpg",
    } as any)

    const response: ApiResponse<{ avatar: string }> = await this.apisauce.patch(
      "/users/profile/photo",
      formData,
      {
        headers: {
          "Content-Type": "multipart/form-data",
          "Authorization": `Bearer ${token}`,
        },
      },
    )
    log("Update photo response:", {
      status: response.status,
      ok: response.ok,
      problem: response.problem,
    })

    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) {
        log("Update photo failed:", problem)
        return problem
      }
    }

    try {
      const data = response.data
      if (!data?.avatar) throw new Error("No photo URL received from the server")

      log("Update photo successful:", { avatar: data.avatar })
      return { kind: "ok", avatar: data.avatar }
    } catch (e) {
      if (__DEV__ && e instanceof Error) {
        log("Update photo data error:", { error: e.message, data: response.data })
      }
      return { kind: "bad-data" }
    }
  }
  /**
   * Refreshes the access token using a refresh token
   */
  async refreshToken(
    refreshToken: string,
  ): Promise<{ kind: "ok"; tokens: AuthResponse } | GeneralApiProblem> {
    log("Refreshing token")

    const response: ApiResponse<AuthResponse> = await this.apisauce.post("/auth/refresh-token", {
      refreshToken,
    })
    log("Token refresh response:", {
      status: response.status,
      ok: response.ok,
      problem: response.problem,
    })

    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) {
        log("Token refresh failed:", problem)
        return problem
      }
      return { kind: "unknown", temporary: true }
    }

    try {
      const tokens = response.data
      if (!tokens) throw new Error("No data received from the server")

      // Set the new tokens and headers
      this.setAuthTokens(tokens.accessToken, tokens.refreshToken)

      return { kind: "ok", tokens }
    } catch (e) {
      if (__DEV__ && e instanceof Error) {
        log("Token refresh data error:", { error: e.message, data: response.data })
      }
      return { kind: "bad-data" }
    }
  }

  /**
   * Gets the current user's profile
   */
  async getProfile(): Promise<{ kind: "ok"; user: User } | GeneralApiProblem> {
    log("Fetching user profile")

    const response: ApiResponse<User> = await this.apisauce.get("/users")
    log("Get profile response:", {
      status: response.status,
      ok: response.ok,
      problem: response.problem,
    })

    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) {
        log("Get profile failed:", problem)
        return problem
      }
    }

    try {
      const user = response.data
      if (!user) throw new Error("No data received from the server")

      log("Get profile successful:", { userId: user.id })
      return { kind: "ok", user }
    } catch (e) {
      if (__DEV__ && e instanceof Error) {
        log("Get profile data error:", { error: e.message, data: response.data })
      }
      return { kind: "bad-data" }
    }
  }

  async resendVerificationCode(): Promise<{ kind: "ok" } | GeneralApiProblem> {
    const response: ApiResponse<any> = await this.apisauce.post(`/users/resend-verification`)

    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }

    return { kind: "ok" }
  }

  async forgotPassword(email: string): Promise<{ kind: "ok" } | GeneralApiProblem> {
    const response = await this.apisauce.post("/users/forgot-password", { email })

    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
    }

    return { kind: "ok" }
  }

  async resetPassword(
    code: string,
    password: string,
    confirmPassword: string,
  ): Promise<{ kind: "ok" } | GeneralApiProblem> {
    const response = await this.apisauce.post(`/auth/reset-password`, {
      code,
      password,
      confirmPassword,
    })

    if (response.ok) {
      return { kind: "ok" }
    } else {
      const problem = getGeneralApiProblem(response)
      if (problem) return problem
      return { kind: "unknown", temporary: true }
    }
  }

  async setAuthTokens(accessToken: string, refreshToken: string) {
    log("Setting auth tokens:", { accessToken }); // Optional logging
    this.accessToken = accessToken
    this.refreshTokenValue = refreshToken
    this.apisauce.setHeader(AUTH_HEADERS.AUTHORIZATION, `Bearer ${accessToken}`)
    // this.apisauce.deleteHeader(AUTH_HEADERS.USER_GROUPS) // Optional, if not needed
    await AsyncStorage.setItem(STORAGE_KEYS.AUTH_TOKENS, JSON.stringify({ accessToken, refreshToken }))
  }
  
  async clearAuthTokens() {
    this.accessToken = null
    this.refreshTokenValue = null
    this.apisauce.deleteHeader(AUTH_HEADERS.AUTHORIZATION)
    this.apisauce.deleteHeader(AUTH_HEADERS.USER_GROUPS)
    await AsyncStorage.removeItem(STORAGE_KEYS.AUTH_TOKENS)
  }
}

// Singleton instance of the API for convenience
export const api = new Api()
