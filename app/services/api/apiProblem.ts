import { ApiResponse } from "apisauce"
import { API_ERRORS, ApiError } from "./api"

export type GeneralApiProblem =
  /**
   * Times up.
   */
  | { kind: "timeout"; temporary: true; error?: string }
  /**
   * Cannot connect to the server for some reason.
   */
  | { kind: "cannot-connect"; temporary: true; error?: string }
  /**
   * The server experienced a problem. Any 5xx error.
   */
  | { kind: "server"; error?: string }
  /**
   * We're not allowed because we haven't identified ourself. This is 401.
   */
  | { kind: "unauthorized"; error?: string }
  /**
   * We don't have access to perform that request. This is 403.
   */
  | { kind: "forbidden"; error?: string }
  /**
   * Unable to find that resource.  This is a 404.
   */
  | { kind: "not-found"; error?: string }
  /**
   * All other 4xx series errors.
   */
  | { kind: "rejected"; error?: any }
  /**
   * Something truly unexpected happened. Most likely can try again. This is a catch all.
   */
  | { kind: "unknown"; temporary: true; error?: string }
  /**
   * The data we received is not in the expected format.
   */
  | { kind: "bad-data"; error?: string }
  | { kind: ApiError; error?: string }

/**
 * Attempts to get a common cause of problems from an api response.
 *
 * @param response The api response.
 */
export function getGeneralApiProblem(response: ApiResponse<any>): GeneralApiProblem | null {
  if ((response as any).customProblem) {
    return { kind: (response as any).customProblem }
  }

  switch (response.problem) {
    case "CONNECTION_ERROR":
      return { kind: API_ERRORS.NETWORK_ERROR }
    case "NETWORK_ERROR":
      return { kind: API_ERRORS.NETWORK_ERROR }
    case "TIMEOUT_ERROR":
      return { kind: API_ERRORS.NETWORK_ERROR }
    case "SERVER_ERROR":
      return { kind: "server" }
    case "UNKNOWN_ERROR":
      return { kind: API_ERRORS.UNKNOWN_ERROR }
    case "CLIENT_ERROR":
      switch (response.status) {
        case 400:
          return { kind: "rejected", error: response.data }
        case 401:
          return { kind: API_ERRORS.INVALID_CREDENTIALS }
        case 403:
          return { kind: "forbidden" }
        case 404:
          return { kind: API_ERRORS.USER_NOT_FOUND }
        default:
          return { kind: "rejected", error: response.data }
      }
    case "CANCEL_ERROR":
      return null
  }

  return null
}
