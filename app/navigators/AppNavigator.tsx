import React from "react"
/**
 * The app navigator (formerly "AppNavigator" and "MainNavigator") is used for the primary
 * navigation flows of your app.
 * Generally speaking, it will contain an auth flow (registration, login, forgot password)
 * and a "main" flow which the user will use once logged in.
 */
import { NavigationContainer, NavigationProp, useNavigation } from "@react-navigation/native"
import { createNativeStackNavigator, NativeStackScreenProps } from "@react-navigation/native-stack"
import { observer } from "mobx-react-lite"
import * as Screens from "@/screens"
import Config from "../config"
import { useStores } from "../models"
import { navigationRef, useBackButtonHandler, resetRoot } from "./navigationUtilities"
import { useAppTheme, useThemeProvider } from "@/utils/useAppTheme"
import { ComponentProps } from "react"
import { Drawer } from "react-native-drawer-layout"
import { isRTL } from "@/i18n"
import { useState, useEffect } from "react"
import { View, ViewStyle, Text, TouchableOpacity } from "react-native"
import { DrawerIconButton } from "@/screens/DemoShowroomScreen/DrawerIconButton"
import { colors, spacing } from "@/theme"
import { useSafeAreaInsets } from "react-native-safe-area-context"
import { Ionicons } from "@expo/vector-icons"
import { useColorScheme } from "react-native"
import { navigate } from "./navigationUtilities"
import { DarkTheme, DefaultTheme } from "@react-navigation/native"
import { TxKeyPath } from "@/i18n"

/**
 * This type allows TypeScript to know what routes are defined in this navigator
 * as well as what properties (if any) they might take when navigating to them.
 *
 * If no params are allowed, pass through `undefined`. Generally speaking, we
 * recommend using your MobX-State-Tree store(s) to keep application state
 * rather than passing state through navigation params.
 *
 * For more information, see this documentation:
 *   https://reactnavigation.org/docs/params/
 *   https://reactnavigation.org/docs/typescript#type-checking-the-navigator
 *   https://reactnavigation.org/docs/typescript/#organizing-types
 */
export type AppStackParamList = {
  Welcome: undefined
  Login: undefined
  Register: undefined
  ForgotPassword: undefined
  ResetPassword: { email: string }
  EmailVerification: undefined
  Success: {
    title: TxKeyPath
    message: TxKeyPath
    onClose: () => void
    visible?: boolean
  }
  Profile: undefined
  EditProfile: undefined
  ChoosePhoto: undefined
}

/**
 * This is a list of all the route names that will exit the app if the back button
 * is pressed while in that screen. Only affects Android.
 */
const exitRoutes = Config.exitRoutes

export type AppStackScreenProps<T extends keyof AppStackParamList> = NativeStackScreenProps<
  AppStackParamList,
  T
>

// Documentation: https://reactnavigation.org/docs/stack-navigator/
const Stack = createNativeStackNavigator<AppStackParamList>()

const AuthGuard = observer(({ children }: { children: React.ReactNode }) => {
  const { userStore } = useStores()
  
  useEffect(() => {
    if (!userStore.currentUser || !userStore.accessToken) {
      resetRoot({
        index: 0,
        routes: [{ name: "Login" }],
      })
    }
  }, [userStore.currentUser, userStore.accessToken])

  return userStore.currentUser && userStore.accessToken ? <>{children}</> : null
})

const AuthStack = observer(function AuthStack() {
  return (
    <Stack.Navigator
      screenOptions={{ 
        headerShown: false,
        navigationBarColor: colors.background,
      }}
      initialRouteName="Login"
    >
      <Stack.Screen name="Login" component={Screens.LoginScreen} />
      <Stack.Screen name="Register" component={Screens.RegisterScreen} />
      <Stack.Screen name="ForgotPassword" component={Screens.ForgotPasswordScreen} />
      <Stack.Screen name="ResetPassword" component={Screens.ResetPasswordScreen} />
      <Stack.Screen name="EmailVerification" component={Screens.EmailVerificationScreen} />
      <Stack.Screen name="Success">
        {(props: AppStackScreenProps<"Success">) => (
          <Screens.SuccessScreen
            title={props.route.params.title}
            message={props.route.params.message}
            onClose={props.route.params.onClose}
            visible={props.route.params.visible ?? true}
          />
        )}
      </Stack.Screen>
    </Stack.Navigator>
  )
})

const MainStack = observer(function MainStack() {
  return (
    <Stack.Navigator
      screenOptions={{ 
        headerShown: false,
        navigationBarColor: colors.background,
      }}
      initialRouteName="Profile"
    >
      <Stack.Screen name="Profile">
        {(props: AppStackScreenProps<"Profile">) => (
          <AuthGuard>
            <Screens.ProfileScreen {...props} />
          </AuthGuard>
        )}
      </Stack.Screen>
      
      <Stack.Screen name="EditProfile">
        {(props: AppStackScreenProps<"EditProfile">) => (
          <AuthGuard>
            <Screens.EditProfileScreen {...props} />
          </AuthGuard>
        )}
      </Stack.Screen>
      
      <Stack.Screen name="ChoosePhoto">
        {(props: AppStackScreenProps<"ChoosePhoto">) => (
          <AuthGuard>
            <Screens.ChoosePhotoScreen {...props} />
          </AuthGuard>
        )}
      </Stack.Screen>
    </Stack.Navigator>
  )
})

const DrawerContent = observer(function DrawerContent({ onClose }: { onClose: () => void }) {
  const insets = useSafeAreaInsets()
  const navigation = useNavigation<NavigationProp<AppStackParamList>>()
  const { userStore } = useStores()
  
  const $drawerContainer: ViewStyle = {
    flex: 1,
    paddingTop: insets.top,
    paddingBottom: insets.bottom,
    backgroundColor: colors.background,
  }

  const $header: ViewStyle = {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    marginTop: spacing.xs,
  }

  const $closeButton: ViewStyle = {
    padding: spacing.xs,
    backgroundColor: "transparent",
  }

  const $drawerItem: ViewStyle = {
    padding: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  }

  const handleProfilePress = () => {
    onClose()
    navigation?.navigate("Profile")
  }

  const handleLogout = () => {
    onClose()
    userStore.logout()
  }

  return (
    <View style={$drawerContainer}>
      <View style={$header}>
        <Text style={{ color: colors.text, fontSize: 18, fontWeight: "600" }}>Menu</Text>
        <TouchableOpacity 
          style={$closeButton} 
          onPress={onClose}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <Ionicons name="close" size={24} color={colors.text} />
        </TouchableOpacity>
      </View>
      <TouchableOpacity style={$drawerItem} onPress={handleProfilePress}>
        <Text style={{ color: colors.text }}>Profile</Text>
      </TouchableOpacity>
      <TouchableOpacity style={$drawerItem} onPress={handleLogout}>
        <Text style={{ color: colors.text }}>Logout</Text>
      </TouchableOpacity>
    </View>
  )
})

const DrawerNavigator = observer(function DrawerNavigator() {
  const [open, setOpen] = useState(false)
  const insets = useSafeAreaInsets()
  const { userStore } = useStores()

  const toggleDrawer = () => setOpen(!open)

  const $container: ViewStyle = {
    flex: 1,
    backgroundColor: colors.background,
  }

  const $menuButton: ViewStyle = {
    position: "absolute",
    top: insets.top + spacing.sm,
    left: spacing.md,
    zIndex: 100,
    elevation: 1,
  }

  // Return AuthStack for unauthenticated users
  if (!userStore.currentUser || !userStore.accessToken) {
    return <AuthStack />
  }

  // Return MainStack with Drawer for authenticated users
  return (
    <Drawer
      open={open}
      onOpen={() => setOpen(true)}
      onClose={() => setOpen(false)}
      drawerType="front"
      drawerPosition={isRTL ? "right" : "left"}
      renderDrawerContent={() => <DrawerContent onClose={() => setOpen(false)} />}
      style={{ flex: 1 }}
      overlayStyle={{ backgroundColor: "rgba(0, 0, 0, 0.5)" }}
    >
      <View style={$container}>
        <View style={$menuButton}>
          <DrawerIconButton onPress={toggleDrawer} />
        </View>
        <MainStack />
      </View>
    </Drawer>
  )
})

export interface NavigationProps extends Partial<ComponentProps<typeof NavigationContainer>> {}

export const AppNavigator = observer(function AppNavigator(props: NavigationProps) {
  const { themeScheme, setThemeContextOverride, ThemeProvider } = useThemeProvider()
  const colorScheme = useColorScheme()
  const { userStore } = useStores()

  useBackButtonHandler((routeName) => exitRoutes.includes(routeName))

  return (
    <ThemeProvider value={{ themeScheme, setThemeContextOverride }}>
      <NavigationContainer 
        ref={navigationRef} 
        theme={colorScheme === "dark" ? DarkTheme : DefaultTheme} 
        {...props}
      >
        <DrawerNavigator />
      </NavigationContainer>
    </ThemeProvider>
  )
})
