import { View, TouchableOpacity, ViewStyle } from "react-native"
import { Text } from "./Text"
import { useAppTheme } from "@/utils/useAppTheme"

export interface SectionHeaderProps {
  title: string
  linkText?: string
  onPressLink?: () => void
}

export function SectionHeader(props: SectionHeaderProps) {
  const { title, linkText, onPressLink } = props
  const { themed } = useAppTheme()

  const $container: ViewStyle = {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 8,
  }

  return (
    <View style={themed($container)}>
      <Text weight="bold" size="lg" text={title} />
      {!!linkText && (
        <TouchableOpacity onPress={onPressLink}>
          <Text weight="medium" size="sm" text={linkText} />
        </TouchableOpacity>
      )}
    </View>
  )
}


