import type { FC } from "react"
import { observer } from "mobx-react-lite"
import { Modal, View, TouchableOpacity, Image } from "react-native"
import { BlurView } from "expo-blur"
import { Text } from "@/components"
import { colors, spacing } from "@/theme"
import { type TextStyle, ImageStyle, ViewStyle } from "react-native"
import { TxKeyPath } from "@/i18n"
import { globalGreenButton, globalGreenButtonText } from "@/theme/globalStyles"

type SuccessScreenProps = {
  title: TxKeyPath
  message: TxKeyPath
  onClose: () => void
  visible: boolean
}

export const SuccessScreen: FC<SuccessScreenProps> = observer(({ title, message, onClose, visible }) => {
  return (
    <Modal transparent visible={visible} animationType="fade">
      <BlurView intensity={50} tint="dark" style={{ flex: 1, justifyContent: "center" }}>
        <View style={$modalContainer}>
          <Image 
            source={require("../../assets/images/good_top_modal.png")} 
            style={$successImage} 
            resizeMode="contain"
          />
          <Text preset="heading" style={$title} tx={title} />
          <Text preset="default" style={$message} tx={message} />
          <TouchableOpacity style={globalGreenButton} onPress={onClose}>
            <Text tx="common:close" style={globalGreenButtonText} />
          </TouchableOpacity>
        </View>
      </BlurView>
    </Modal>
  )
})

const $modalContainer: ViewStyle = {
  backgroundColor: colors.palette.neutral100,
  borderRadius: 16,
  padding: spacing.xl,
  marginHorizontal: spacing.lg,
  alignItems: "center",
}

const $successImage: ImageStyle = {
  width: 60,
  height: 60,
  marginBottom: spacing.md,
}

const $title: TextStyle = {
  color: colors.text,
  textAlign: "center",
  marginBottom: spacing.xs,
  fontSize: 20,
}

const $message: TextStyle = {
  color: colors.text,
  textAlign: "center",
  marginBottom: spacing.md,
  fontSize: 16,
}

