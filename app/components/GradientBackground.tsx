import { LinearGradient } from "expo-linear-gradient"
import { ViewStyle } from "react-native"

interface GradientBackgroundProps {
  colors?: [string, string]
  style?: ViewStyle
}

export function GradientBackground({ 
  colors = ["#7BB285", "#308B3C"] as [string, string],
  style
}: GradientBackgroundProps) {
  return (
    <LinearGradient
      colors={colors}
      style={[{
        position: "absolute",
        left: 0,
        right: 0,
        top: -100,
        bottom: -100,
      }, style]}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
    />
  )
} 