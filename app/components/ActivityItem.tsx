import { ReactElement } from "react"
import { ListItem, ListItemProps } from "./ListItem"
import { IconTypes } from "./Icon"
import { StatusPill, StatusTone } from "./StatusPill"

export interface ActivityItemProps extends Omit<ListItemProps, "leftIcon" | "RightComponent"> {
  icon: IconTypes
  title: string
  subtitle?: string
  statusLabel?: string
  statusTone?: StatusTone
}

export function ActivityItem(props: ActivityItemProps) {
  const { icon, title, subtitle, statusLabel, statusTone = "neutral", ...rest } = props

  const Right: ReactElement | undefined = statusLabel
    ? <StatusPill label={statusLabel} tone={statusTone} />
    : undefined

  return (
    <ListItem
      leftIcon={icon}
      text={title}
      TextProps={{ children: subtitle ? `\n${subtitle}` : undefined }}
      RightComponent={Right}
      height={72}
      {...rest}
    />
  )
}


