import type { FC } from "react"
import { observer } from "mobx-react-lite"
import { Modal, View, TouchableOpacity, Image } from "react-native"
import { BlurView } from "expo-blur"
import { Text } from "@/components"
import { colors, spacing } from "@/theme"
import { type TextStyle, ImageStyle } from "react-native"
import { TxKeyPath } from "@/i18n"
import { globalRedButton, globalRedButtonText } from "@/theme/globalStyles"

type ErrorScreenProps = {
  title: TxKeyPath
  message: TxKeyPath
  onClose: () => void
  visible: boolean
}

export const ErrorScreen: FC<ErrorScreenProps> = observer(({ title, message, onClose, visible }) => {
  return (
    <Modal transparent visible={visible} animationType="fade">
      <BlurView intensity={50} tint="dark" style={{ flex: 1, justifyContent: "center" }}>
        <View style={$modalContainer}>
          <Image 
            source={require("../../assets/images/wrong_top_modal.png")} 
            style={$errorImage} 
          />
          <Text preset="heading" style={$title} tx={title} />
          <Text preset="default" style={$message} tx={message} />
          <TouchableOpacity style={globalRedButton} onPress={onClose}>
            <Text tx="common:close" style={globalRedButtonText} />
          </TouchableOpacity>
        </View>
      </BlurView>
    </Modal>
  )
})

const $modalContainer = {
  backgroundColor: colors.palette.neutral100,
  borderRadius: 16,
  padding: spacing.xl,
  marginHorizontal: spacing.lg,
  alignItems: "center" as const,
}

const $errorImage: ImageStyle = {
  width: 60,
  height: 60,
  marginBottom: spacing.md,
}

const $title: TextStyle = {
  color: colors.text,
  textAlign: "center",
  marginBottom: spacing.xs,
  fontSize: 20,
}

const $message: TextStyle = {
  color: colors.text,
  textAlign: "center",
  marginBottom: spacing.md,
  fontSize: 16,
}

