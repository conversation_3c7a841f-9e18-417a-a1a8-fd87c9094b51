import { View } from "react-native"
import { spacing, colors } from "../theme"

interface PageIndicatorProps {
  totalPages: number
  currentPage: number
}

export function PageIndicator({ totalPages, currentPage }: PageIndicatorProps) {
  return (
    <View
      style={{
        flexDirection: "row",
        justifyContent: "center",
        alignItems: "center",
        paddingVertical: spacing.md,
      }}
    >
      {Array.from({ length: totalPages }).map((_, index) => (
        <View
          key={index}
          style={{
            width: 8,
            height: 8,
            borderRadius: 4,
            backgroundColor: currentPage === index ? colors.primary : colors.palette.neutral400,
            marginHorizontal: spacing.xxs,
          }}
        />
      ))}
    </View>
  )
}

