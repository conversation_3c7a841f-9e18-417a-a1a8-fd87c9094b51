import { View, ViewStyle } from "react-native"
import { Text } from "./Text"
import { useAppTheme } from "@/utils/useAppTheme"

export type StatusTone = "success" | "danger" | "neutral"

export interface StatusPillProps {
  label: string
  tone?: StatusTone
}

export function StatusPill(props: StatusPillProps) {
  const { label, tone = "neutral" } = props
  const {
    theme: { colors, spacing },
    themed,
  } = useAppTheme()

  const backgroundByTone = {
    success: colors.successBackground,
    danger: colors.errorBackground,
    neutral: colors.palette.neutral200,
  } as const

  const textColorByTone = {
    success: colors.success,
    danger: colors.error,
    neutral: colors.text,
  } as const

  const $container: ViewStyle = {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xxs,
    borderRadius: 999,
    backgroundColor: backgroundByTone[tone],
    alignSelf: "center",
  }

  return (
    <View style={themed($container)}>
      <Text size="xs" weight="medium" text={label} style={{ color: textColorByTone[tone] }} />
    </View>
  )
}


