import { Pressable, PressableProps, View, ViewStyle } from "react-native"
import { Icon, IconTypes } from "./Icon"
import { Text } from "./Text"
import { useAppTheme } from "@/utils/useAppTheme"

export type TileSize = "sm" | "lg"

export interface ActionTileProps extends PressableProps {
  icon: IconTypes
  label: string
  backgroundColor?: string
  tintColor?: string
  size?: TileSize
}

export function ActionTile(props: ActionTileProps) {
  const { icon, label, backgroundColor, tintColor, size = "lg", style, ...rest } = props
  const {
    theme: { spacing, colors },
    themed,
  } = useAppTheme()

  const dimension = size === "lg" ? 144 : 112

  const $container: ViewStyle = {
    width: dimension,
    height: dimension,
    borderRadius: spacing.md,
    backgroundColor: backgroundColor ?? colors.palette.secondary100,
    padding: spacing.md,
    alignItems: "center",
    justifyContent: "center",
  }

  const $iconWrapper: ViewStyle = {
    width: 72,
    height: 72,
    borderRadius: 16,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: spacing.sm,
  }

  return (
    <Pressable {...rest} style={[themed($container), style]}>
      <View style={themed($iconWrapper)}>
        <Icon icon={icon} size={48} color={tintColor ?? colors.palette.neutral700} />
      </View>
      <Text weight="medium" text={label} />
    </Pressable>
  )
}


