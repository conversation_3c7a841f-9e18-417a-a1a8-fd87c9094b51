import { type ViewStyle, Modal, View, type TextStyle } from "react-native"
import { Button, Text } from "."
import { colors, spacing } from "../theme"
import { TxKeyPath } from "@/i18n"

interface ErrorModalProps {
  isVisible: boolean
  onClose: () => void
  message: string
}

export function ErrorModal({ isVisible, onClose, message }: ErrorModalProps) {
  return (
    <Modal visible={isVisible} transparent animationType="fade" onRequestClose={onClose}>
      <View style={$overlay}>
        <View style={$content}>
          <Text tx={message as TxKeyPath} style={$message} />
          <Button tx="common:ok" style={$button} onPress={onClose} />
        </View>
      </View>
    </Modal>
  )
}

const $overlay: ViewStyle = {
  flex: 1,
  backgroundColor: colors.overlay,
  justifyContent: "center",
  alignItems: "center",
}

const $content: ViewStyle = {
  backgroundColor: colors.background,
  borderRadius: spacing.sm,
  padding: spacing.lg,
  width: "80%",
}

const $message: TextStyle = {
  textAlign: "center",
  marginBottom: spacing.md,
}

const $button: ViewStyle = {
  minWidth: 120,
}

