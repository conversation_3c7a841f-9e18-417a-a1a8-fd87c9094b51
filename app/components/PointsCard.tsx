import { View, ViewStyle } from "react-native"
import { Text } from "./Text"
import { useAppTheme } from "@/utils/useAppTheme"

export interface PointsCardProps {
  points: number
  deltaPercent?: number
  ctaText?: string
  onPressCta?: () => void
}

export function PointsCard(props: PointsCardProps) {
  const { points, deltaPercent, ctaText } = props
  const {
    theme: { spacing, colors },
    themed,
  } = useAppTheme()

  const $container: ViewStyle = {
    borderRadius: spacing.md,
    borderStyle: "dashed",
    borderWidth: 1,
    borderColor: colors.palette.neutral400,
    padding: spacing.md,
    width: 144,
    height: 144,
    backgroundColor: colors.palette.neutral100,
    justifyContent: "space-between",
  }

  const $row: ViewStyle = {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  }

  return (
    <View style={themed($container)}>
      <View style={themed($row)}>
        <Text size="xs" text="PUAN" />
        {deltaPercent !== undefined && (
          <Text size="xs" text={`+${deltaPercent.toFixed(2)}%`} />
        )}
      </View>
      <Text size="xl" weight="bold" text={String(points)} />
      <View style={themed($row)}>
        <Text size="sm" text={ctaText || "Hediyelerim"} />
      </View>
    </View>
  )
}


