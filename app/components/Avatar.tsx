import { Image, ImageStyle, View, ViewStyle } from "react-native"
import { Text } from "./Text"
import { useAppTheme } from "@/utils/useAppTheme"

export interface AvatarProps {
  uri?: string
  initials?: string
  size?: number
}

export function Avatar(props: AvatarProps) {
  const { uri, initials, size = 36 } = props
  const {
    theme: { colors },
    themed,
  } = useAppTheme()

  const $container: ViewStyle = {
    width: size,
    height: size,
    borderRadius: size / 2,
    overflow: "hidden",
    backgroundColor: colors.palette.neutral300,
    alignItems: "center",
    justifyContent: "center",
  }

  const $image: ImageStyle = {
    width: size,
    height: size,
  }

  if (uri) {
    return (
      <View style={themed($container)}>
        <Image source={{ uri }} style={$image} />
      </View>
    )
  }

  return (
    <View style={themed($container)}>
      <Text weight="medium" text={initials || ""} />
    </View>
  )
}


