import { FC } from "react"
import { observer } from "mobx-react-lite"
import { Modal, View, TouchableOpacity, ScrollView } from "react-native"
import { BlurView } from "expo-blur"
import { Text } from "@/components"
import { colors, spacing } from "@/theme"
import { type TextStyle, ViewStyle } from "react-native"
import { TxKeyPath } from "@/i18n"
import { useWindowDimensions } from 'react-native'

type LegalScreenProps = {
  title: TxKeyPath
  content: TxKeyPath
  onClose: () => void
  visible: boolean
}

export const LegalScreen: FC<LegalScreenProps> = observer(({ title, content, onClose, visible }) => {
  const { width } = useWindowDimensions()

  return (
    <Modal transparent visible={visible} animationType="slide">
      <View style={$modalContainer}>
        <View style={$headerContainer}>
          <Text tx={title} style={$title} />
          <TouchableOpacity onPress={onClose} style={$closeButton}>
            <Text text="✕" style={$closeButtonText} />
          </TouchableOpacity>
        </View>
        <View style={$scrollContainer}>
          <ScrollView 
            showsVerticalScrollIndicator={true}
            bounces={true}
          >
            <Text tx={content} style={$content} />
          </ScrollView>
        </View>
      </View>
    </Modal>
  )
})

const $modalContainer: ViewStyle = {
  position: 'absolute',
  top: '5%',
  left: spacing.sm,
  right: spacing.sm,
  height: '90%',
  backgroundColor: '#308B3C',
  borderRadius: 11,
  overflow: 'hidden',
}

const $headerContainer: ViewStyle = {
  flexDirection: 'row',
  justifyContent: 'space-between',
  alignItems: 'center',
  padding: spacing.md,
  borderBottomWidth: 1,
  borderBottomColor: 'rgba(255,255,255,0.1)',
}

const $title: TextStyle = {
  color: colors.palette.neutral100,
  fontSize: 20,
  fontWeight: 'bold',
}

const $closeButton: ViewStyle = {
  padding: 0,
  width: 40,
  height: 40,
  alignItems: 'center',
  justifyContent: 'center',
  margin: spacing.xs,
}

const $closeButtonText: TextStyle = {
  color: colors.palette.neutral100,
  fontSize: 24,
  fontWeight: 'bold',
  textAlign: 'center',
  lineHeight: 24,
}

const $scrollContainer: ViewStyle = {
  flex: 1,
  paddingHorizontal: spacing.md,
}

const $content: TextStyle = {
  color: colors.palette.neutral100,
  lineHeight: 24,
  paddingVertical: spacing.md,
} 