import { View } from "react-native"
import { Screen } from "@/components/Screen"
import { <PERSON><PERSON> } from "@/components/Header"
import { Avatar } from "@/components/Avatar"
import { ActionTile } from "@/components/ActionTile"
import { PointsCard } from "@/components/PointsCard"
import { SectionHeader } from "@/components/SectionHeader"
import { ActivityItem } from "@/components/ActivityItem"
import { useAppTheme } from "@/utils/useAppTheme"

export function HomeScreen() {
  const {
    theme: { spacing },
    themed,
  } = useAppTheme()

  return (
    <Screen preset="scroll" contentContainerStyle={{ padding: spacing.md }}>
      <Header
        leftIcon="menu"
        rightIcon={undefined}
        RightActionComponent={<Avatar initials="OK" />}
        title=""
      />
      <View style={themed({ flexDirection: "row", flexWrap: "wrap", gap: spacing.md })}>
        <ActionTile icon="view" label="Fiş fotoğrafı çek" />
        <ActionTile icon="view" label="Tüm çekimler" backgroundColor={"#DFEBD5"} />
        <ActionTile icon="community" label="Mükellefler" />
        <PointsCard points={1500} deltaPercent={2.4} />
      </View>

      <View style={themed({ marginTop: spacing.lg })}>
        <SectionHeader title="Son Hareketler" linkText="Hepsini gör" onPressLink={() => {}} />
        <View style={themed({ gap: spacing.xs })}>
          <ActivityItem icon="view" title="Piyote A.Ş." subtitle="Aralık ayı fiş tablosu 24.12.2024 12:30" />
          <ActivityItem icon="view" title="Piyote A.Ş." subtitle="Aralık ayı fiş çekimi 24.12.2024 12:25" />
          <ActivityItem icon="view" title="Dama LTD." subtitle="Aralık ayı fiş çekimi 23.12.2024 14:10" />
        </View>
      </View>
    </Screen>
  )
}

export default HomeScreen


