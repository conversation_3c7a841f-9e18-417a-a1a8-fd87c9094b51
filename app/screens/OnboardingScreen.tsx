import * as React from "react"
import {
  View,
  TouchableOpacity,
  Text,
  ScrollView,
  Dimensions,
  ImageBackground,
  type NativeSyntheticEvent,
  type NativeScrollEvent,
} from "react-native"
import { observer } from "mobx-react-lite"
import type { NativeStackScreenProps } from "@react-navigation/native-stack"
import type { AppStackParamList } from "../navigators"
import { PageIndicator } from "../components/PageIndicator"
import { colors, spacing, typography } from "../theme"
import { useTranslation } from "react-i18next"
import { Launch1 } from "./Onboarding/Launch1"
import { Launch2 } from "./Onboarding/Launch2"
import { Launch3 } from "./Onboarding/Launch3"

const { width, height } = Dimensions.get("window")

type OnboardingScreenProps = NativeStackScreenProps<AppStackParamList, "Onboarding">

const slides = [
  { id: 1, component: Launch1 },
  { id: 2, component: Launch2 },
  { id: 3, component: Launch3 },
]

export const OnboardingScreen = observer(function OnboardingScreen({ navigation }: OnboardingScreenProps) {
  const [currentPage, setCurrentPage] = React.useState(0)
  const scrollViewRef = React.useRef<ScrollView>(null)
  const { t } = useTranslation()

  const handleScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const offsetX = event.nativeEvent.contentOffset.x
    const page = Math.round(offsetX / width)
    setCurrentPage(page)
  }

  const handleNext = () => {
    if (currentPage === slides.length - 1) {
      navigation.replace("Login" as keyof AppStackParamList)
    } else {
      scrollViewRef.current?.scrollTo({
        x: (currentPage + 1) * width,
        animated: true,
      })
    }
  }

  const handleScrollBegin = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const offsetX = event.nativeEvent.contentOffset.x
    const currentIndex = Math.round(offsetX / width)
    const velocityX = event.nativeEvent.velocity?.x ?? 0

    if (
      (currentIndex === 0 && velocityX > 0) ||
      (currentIndex === slides.length - 1 && velocityX < 0)
    ) {
      scrollViewRef.current?.scrollTo({
        x: currentIndex * width,
        animated: true,
      })
    }
  }

  const buttonText = currentPage === slides.length - 1
    ? t("onboarding:buttons:getStarted")
    : t("onboarding:buttons:continue")

  return (
    <View style={{ flex: 1, backgroundColor: colors.background }}>
      <TouchableOpacity
        onPress={() => navigation.replace("Login" as keyof AppStackParamList)}
        style={{
          position: 'absolute',
          top: spacing.lg,
          right: spacing.lg,
          zIndex: 1,
          padding: spacing.xs,
        }}
      >
        <Text style={[typography.body, { color: colors.text }]}>
          {t("onboarding:buttons:skip")}
        </Text>
      </TouchableOpacity>

      <ScrollView
        ref={scrollViewRef}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onScroll={handleScroll}
        onScrollBeginDrag={handleScrollBegin}
        scrollEventThrottle={16}
        bounces={false}
      >
        {slides.map((Slide, index) => (
          <View key={Slide.id} style={{ width, flex: 1 }}>
            <Slide.component />
          </View>
        ))}
      </ScrollView>

      <View style={{ padding: spacing.lg }}>
        <PageIndicator totalPages={slides.length} currentPage={currentPage} />

        <TouchableOpacity
          onPress={handleNext}
          style={{
            backgroundColor: colors.palette.primary500,
            paddingVertical: spacing.md,
            borderRadius: 8,
            alignItems: "center",
          }}
        >
          <Text style={[typography.button, { color: colors.palette.neutral100 }]}>
            {buttonText}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  )
})

