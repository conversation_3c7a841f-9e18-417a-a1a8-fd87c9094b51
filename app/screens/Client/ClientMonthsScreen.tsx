import { View } from "react-native"
import { Screen } from "@/components/Screen"
import { Header } from "@/components/Header"
import { ActionTile } from "@/components/ActionTile"
import { Button } from "@/components/Button"
import { useAppTheme } from "@/utils/useAppTheme"

const MONTHS = [
  "Aralık 2024",
  "Kasım 2024",
  "Ekim 2024",
  "Eylül 2024",
  "Ağustos 2024",
  "Temmuz 2024",
]

export function ClientMonthsScreen() {
  const {
    theme: { spacing },
    themed,
  } = useAppTheme()

  return (
    <Screen preset="scroll" contentContainerStyle={{ padding: spacing.md }}>
      <Header title="Piyote A.Ş." rightText="" />

      <View style={themed({ alignItems: "flex-end", marginBottom: spacing.md })}>
        <Button text="Mükellefi çıkar" />
      </View>

      <View style={themed({ flexDirection: "row", flexWrap: "wrap" })}>
        {MONTHS.map((m) => (
          <View key={m} style={themed({ margin: spacing.sm })}>
            <ActionTile icon="view" label={m} size="sm" />
          </View>
        ))}
      </View>
    </Screen>
  )
}

export default ClientMonthsScreen


