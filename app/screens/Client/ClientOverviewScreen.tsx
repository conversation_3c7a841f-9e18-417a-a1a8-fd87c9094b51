import { View } from "react-native"
import { Screen } from "@/components/Screen"
import { Header } from "@/components/Header"
import { ActionTile } from "@/components/ActionTile"
import { TextField } from "@/components/TextField"
import { ActivityItem } from "@/components/ActivityItem"
import { useAppTheme } from "@/utils/useAppTheme"

export function ClientOverviewScreen() {
  const {
    theme: { spacing },
    themed,
  } = useAppTheme()

  return (
    <Screen preset="scroll" contentContainerStyle={{ padding: spacing.md }}>
      <Header title="Piyote A.Ş." rightText="" />

      <View style={themed({ flexDirection: "row", gap: spacing.md, marginBottom: spacing.md })}>
        <ActionTile icon="view" label="Tüm tablolar" />
        <ActionTile icon="podcast" label="Tüm çekimler" backgroundColor={"#E6EEFF"} />
      </View>

      <TextField placeholder="tarih ile ara" />

      <View style={themed({ gap: spacing.xs, marginTop: spacing.md })}>
        <ActivityItem icon="view" title="Aralık 2024" statusLabel="Tamamlandı" statusTone="success" />
        <ActivityItem icon="view" title="Kasım 2024" statusLabel="Tamamlandı" statusTone="success" />
        <ActivityItem icon="view" title="Ekim 2024" statusLabel="Eksik" statusTone="danger" />
        <ActivityItem icon="view" title="Eylül 2024" statusLabel="Eksik" statusTone="danger" />
      </View>
    </Screen>
  )
}

export default ClientOverviewScreen


