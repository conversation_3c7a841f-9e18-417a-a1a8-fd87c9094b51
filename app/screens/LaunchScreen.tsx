import * as React from "react"
import { View, Image, Animated, ImageBackground } from "react-native"
import { observer } from "mobx-react-lite"
import type { NativeStackScreenProps } from "@react-navigation/native-stack"
import type { AppStackParamList } from "../navigators"
import { colors, spacing } from "../theme"

type LaunchScreenProps = NativeStackScreenProps<AppStackParamList, "Launch">

export const LaunchScreen = observer(function LaunchScreen({ navigation }: LaunchScreenProps) {
  const fadeAnim = React.useRef(new Animated.Value(0)).current

  React.useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 1000,
      useNativeDriver: true,
    }).start()

    const timer = setTimeout(() => {
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
      }).start(() => {
        navigation.replace("Onboarding")
      })
    }, 10000)

    return () => {
      clearTimeout(timer)
    }
  }, [navigation, fadeAnim])

  return (
    <View style={{ flex: 1 }}>
      <ImageBackground
        source={require("../../assets/images/radial-bg.png")}
        style={{
          position: 'absolute',
          width: '100%',
          height: '100%',
        }}
        resizeMode="cover"
      />
      <Animated.View
        style={{
          flex: 1,
          backgroundColor: 'transparent',
          justifyContent: "center",
          alignItems: "center",
          padding: spacing.lg,
          opacity: fadeAnim,
        }}
      >
        <Image
          source={require("../../assets/images/logo.png")}
          style={{
            width: 150,
            height: 150,
            resizeMode: "contain",
          }}
        />
      </Animated.View>
    </View>
  )
})

