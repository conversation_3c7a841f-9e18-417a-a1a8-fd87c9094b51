import { StyleSheet } from "react-native"
import { spacing } from "@/theme"

const colors = { primary: "#2B9B4A",
  accountantButton: "#40C3D9",
  taxpayerButton: "#384862",
  white: "#FFFFFF",
}
export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.primary,
  },
  contentContainer: {
    flex: 1,
    alignItems: "center",
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.xxl,
  },
  logo: {
    width: 120,
    height: 120,
    resizeMode: "contain",
    marginBottom: spacing.xl,
  },
  title: {
    fontSize: 32,
    color: colors.white,
    marginBottom: spacing.xxl,
  },
  buttonContainer: {
    width: "100%",
    gap: spacing.md,
    marginBottom: spacing.xxxl,
  },
  accountantButton: {
    backgroundColor: colors.accountantButton,
    borderRadius: 12,
    paddingVertical: spacing.md,
  },
  taxpayerButton: {
    backgroundColor: colors.taxpayerButton,
    borderRadius: 12,
    paddingVertical: spacing.md,
  },
  buttonText: {
    fontSize: 16,
    color: colors.white,
  },
  subtitle: {
    fontSize: 18,
    color: colors.white,
    position: "absolute",
    bottom: spacing.xxxl,
  },
})

