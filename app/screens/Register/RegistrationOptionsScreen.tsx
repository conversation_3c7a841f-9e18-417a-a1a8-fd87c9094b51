import type { FC } from "react"
import { View, Image } from "react-native"
import { Text, Button, Screen } from "@/components"
import { styles } from "./styles"

export const RegistrationOptionsScreen: FC = () => {
  return (
    <Screen style={styles.container} preset="scroll">
      <View style={styles.contentContainer}>
        <Image source={require("../../../assets/images/logo.png")} style={styles.logo} />

        <Text tx="registrationOptions:title" style={styles.title} />

        <View style={styles.buttonContainer}>
          <Button tx="registrationOptions:accountant" style={styles.accountantButton} textStyle={styles.buttonText} />

          <Button tx="registrationOptions:taxpayer" style={styles.taxpayerButton} textStyle={styles.buttonText} />
        </View>

        <Text tx="registrationOptions:subtitle" style={styles.subtitle} />
      </View>
    </Screen>
  )
}

