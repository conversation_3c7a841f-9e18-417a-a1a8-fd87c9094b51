"use client"

import { type FC, useState } from "react"
import { observer } from "mobx-react-lite"
import { type ViewStyle, View, type TextStyle, type ImageStyle, Image, TouchableOpacity, ActivityIndicator, Platform, KeyboardAvoidingView, ScrollView, useWindowDimensions } from "react-native"
import { Button, Icon, Screen, TextField, Text, GradientBackground, SuccessScreen, LegalScreen } from "@/components"
import { colors, spacing } from "@/theme"
import { useStores } from "@/models"
import type { AppStackScreenProps } from "@/navigators"
import { globalButton, globalButtonText, globalTextInput } from "@/theme/globalStyles"
import { TxKeyPath } from "@/i18n"
import { ErrorScreen } from "@/components/ErrorScreen"

interface RegisterScreenProps extends AppStackScreenProps<"Register"> {}

export const RegisterScreen: FC<RegisterScreenProps> = observer(function RegisterScreen(_props) {
  const { userStore } = useStores()
  const { navigation } = _props
  const { width: windowWidth, height: windowHeight } = useWindowDimensions()

  // Calculate responsive sizes
  const logoSize = Math.min(windowWidth * 0.3, 150) // Cap logo size on large screens
  const formMaxWidth = Math.min(windowWidth * 0.9, 400) // Cap form width on large screens

  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [isPasswordVisible, setIsPasswordVisible] = useState(false)
  const [isConfirmPasswordVisible, setIsConfirmPasswordVisible] = useState(false)
  const [isSuccessVisible, setIsSuccessVisible] = useState(false)
  const [isLegalVisible, setIsLegalVisible] = useState(false)
  const [isTermsVisible, setIsTermsVisible] = useState(false)
  const [passwordMatchError, setPasswordMatchError] = useState<TxKeyPath>("loginScreen:loginError")
  const [isPasswordErrorVisible, setIsPasswordErrorVisible] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [referrerCode, setReferrerCode] = useState("")

  const validatePasswords = () => {
    if (confirmPassword && password !== confirmPassword) {
      setPasswordMatchError("registerScreen:passwordMatchError" as TxKeyPath)
      setIsPasswordErrorVisible(true)
    } else {
      setIsPasswordErrorVisible(false)
    }
  }

  const handleRegister = async () => {
    if (!email || !password || !confirmPassword) {
      setPasswordMatchError("loginScreen:emptyFieldsError" as TxKeyPath)
      setIsPasswordErrorVisible(true)
      return
    }

    if (password !== confirmPassword) {
      setPasswordMatchError("registerScreen:passwordMatchError" as TxKeyPath)
      setIsPasswordErrorVisible(true)
      return
    }

    //TODO : simdilik basit email kontrolü bilunmu
    const isValidEmail = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
    if (!isValidEmail) {
      setPasswordMatchError("errors:invalidEmail" as TxKeyPath);
      setIsPasswordErrorVisible(true);
      return;
    }

    setIsSubmitting(true)
    try {
      const result = await userStore.register({
        email,
        password,
        passwordRetry: confirmPassword,
        userType: 'B', // Default user type
        code: referrerCode || undefined, // Only include if provided
      })

      if (result.kind === "ok") {
        setIsSuccessVisible(true)
      } else {
        // Handle specific error cases
        const error = result.error;
        
        if (error?.message?.includes("Password is too weak")) {
          setPasswordMatchError("errors:weakPassword" as TxKeyPath);
        } else if (error?.message?.includes("must be longer than or equal to 8")) {
          setPasswordMatchError("errors:passwordTooShort" as TxKeyPath);
        } else if (error === "EMAIL_ALREADY_EXISTS") {
          setPasswordMatchError("registerScreen:emailExists" as TxKeyPath);
        } else if (error === "INVALID_EMAIL") {
          setPasswordMatchError("errors:invalidEmail" as TxKeyPath);
        } else if (error?.statusCode === 500) {
          // Server error
          setPasswordMatchError("errors:serverError" as TxKeyPath);
          console.error("Server error:", error);
        } else {
          setPasswordMatchError("registerScreen:registrationError" as TxKeyPath);
          console.error("Registration error:", error);
        }
        
        setIsPasswordErrorVisible(true);
      }
    } catch (error) {
      console.error("Registration error:", error)
      setPasswordMatchError("registerScreen:registrationError" as TxKeyPath)
      setIsPasswordErrorVisible(true)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleSuccessClose = () => {
    setIsSuccessVisible(false)
    navigation.navigate("EmailVerification")
  }

  return (
    <Screen
      preset="fixed"
      contentContainerStyle={$screenContentContainer}
      safeAreaEdges={["top", "bottom"]}
    >
      <GradientBackground />
      <KeyboardAvoidingView
        style={$keyboardAvoidingViewStyle}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        keyboardVerticalOffset={Platform.OS === "ios" ? 0 : 20}
      >
        <ScrollView
          style={$scrollViewStyle}
          contentContainerStyle={[
            $contentContainer,
            { minHeight: windowHeight * 0.9 },
          ]}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
          bounces={false}
        >
          <View style={[$mainContentContainer, { maxWidth: formMaxWidth, alignSelf: "center" }]}>
            <View style={$logoContainer}>
              <Image 
                source={require("../../../assets/images/logo.png")} 
                style={[$logo, { width: logoSize, height: logoSize }]} 
                resizeMode="contain" 
              />
              <Text tx="registerScreen:title" preset="subheading" style={$title} />
            </View>

            <View style={$formContainer}>
              <TextField
                value={email}
                onChangeText={setEmail}
                containerStyle={$textField}
                autoCapitalize="none"
                autoComplete="email"
                autoCorrect={false}
                keyboardType="email-address"
                labelTx="registerScreen:emailLabel"
                placeholderTx="registerScreen:emailPlaceholder"
                inputWrapperStyle={globalTextInput}
                editable={!isSubmitting}
              />

              <TextField
                value={password}
                onChangeText={setPassword}
                containerStyle={$textField}
                autoCapitalize="none"
                autoComplete="password"
                autoCorrect={false}
                secureTextEntry={!isPasswordVisible}
                labelTx="registerScreen:passwordLabel"
                placeholderTx="registerScreen:passwordPlaceholder"
                RightAccessory={(props) => (
                  <Icon
                    icon={isPasswordVisible ? "view" : "hidden"}
                    color={colors.text}
                    containerStyle={props.style}
                    size={20}
                    onPress={() => setIsPasswordVisible(!isPasswordVisible)}
                  />
                )}
                inputWrapperStyle={globalTextInput}
                editable={!isSubmitting}
              />

              <TextField
                value={confirmPassword}
                onChangeText={setConfirmPassword}
                onBlur={validatePasswords}
                containerStyle={$textField}
                autoCapitalize="none"
                autoComplete="password"
                autoCorrect={false}
                secureTextEntry={!isConfirmPasswordVisible}
                labelTx="registerScreen:confirmPasswordLabel"
                placeholderTx="registerScreen:confirmPasswordPlaceholder"
                RightAccessory={(props) => (
                  <Icon
                    icon={isConfirmPasswordVisible ? "view" : "hidden"}
                    color={colors.text}
                    containerStyle={props.style}
                    size={20}
                    onPress={() => setIsConfirmPasswordVisible(!isConfirmPasswordVisible)}
                  />
                )}
                inputWrapperStyle={globalTextInput}
                editable={!isSubmitting}
              />

              <TextField
                value={referrerCode}
                onChangeText={setReferrerCode}
                containerStyle={$textField}
                autoCapitalize="characters"
                autoCorrect={false}
                labelTx={"registerScreen.referrerCodeLabel" as TxKeyPath}
                placeholderTx={"registerScreen.referrerCodePlaceholder" as TxKeyPath}
                inputWrapperStyle={globalTextInput}
                editable={!isSubmitting}
              />

              {isPasswordErrorVisible && (
                <Text
                  tx={passwordMatchError}
                  style={$errorText}
                  preset="default"
                />
              )}

              <TouchableOpacity 
                style={[globalButton, isSubmitting && { opacity: 0.7 }]} 
                onPress={handleRegister}
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <ActivityIndicator color={colors.palette.neutral100} />
                ) : (
                  <Text tx="registerScreen:createAccount" style={globalButtonText} />
                )}
              </TouchableOpacity>

              <View style={$footerContainer}>
                <Text tx="registerScreen:haveAccount" style={$footerText} />
                <Text
                  tx="registerScreen:login"
                  style={[$footerText, $link]}
                  onPress={() => navigation.navigate("Login")}
                />
              </View>

              <Text
                tx="registerScreen:forgotPassword"
                style={[$footerText, $link, $forgotPassword]}
                onPress={() => navigation.navigate("ForgotPassword")}
              />

              <View style={$termsContainer}>
                <Text style={$termsText}>
                  <Text tx="resetPasswordScreen:termsText" style={$termsTextInner} />
                  {" "}
                  <Text 
                    tx="resetPasswordScreen:termsLink" 
                    style={[$termsTextInner, $link]}
                    onPress={() => setIsTermsVisible(true)}
                  />
                  {" "}
                  <Text tx="resetPasswordScreen:andText" style={$termsTextInner} />
                  {" "}
                  <Text 
                    tx="resetPasswordScreen:privacyLink" 
                    style={[$termsTextInner, $link]}
                    onPress={() => setIsLegalVisible(true)}
                  />
                </Text>
              </View>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>

      <SuccessScreen
        title="registerScreen:successTitle"
        message="registerScreen:successMessage"
        onClose={handleSuccessClose}
        visible={isSuccessVisible}
      />

      <LegalScreen
        title="legal:doc1Title"
        content="legal:doc1Content"
        onClose={() => setIsLegalVisible(false)}
        visible={isLegalVisible}
      />

      <LegalScreen
        title="legal:doc2Title"
        content="legal:doc2Content"
        onClose={() => setIsTermsVisible(false)}
        visible={isTermsVisible}
      />

      <ErrorScreen
        title="common:error"
        message={passwordMatchError}
        onClose={() => setIsPasswordErrorVisible(false)}
        visible={isPasswordErrorVisible}
      />
    </Screen>
  )
})

const $screenContentContainer: ViewStyle = {
  flex: 1,
}

const $keyboardAvoidingViewStyle: ViewStyle = {
  flex: 1,
}

const $scrollViewStyle: ViewStyle = {
  flex: 1,
}

const $contentContainer: ViewStyle = {
  flexGrow: 1,
  paddingHorizontal: spacing.lg,
  paddingVertical: spacing.xl,
  justifyContent: "center",
}

const $mainContentContainer: ViewStyle = {
  width: "100%",
  justifyContent: "center",
  paddingHorizontal: spacing.sm,
}

const $logoContainer: ViewStyle = {
  alignItems: "center",
  marginBottom: spacing.xxl,
}

const $formContainer: ViewStyle = {
  width: "100%",
}

const $logo: ImageStyle = {
  // Size is set dynamically
}

const $title: TextStyle = {
  marginTop: spacing.md,
  color: colors.palette.neutral100,
  textAlign: "center",
}

const $textField: ViewStyle = {
  marginBottom: spacing.lg,
}

const $footerContainer: ViewStyle = {
  flexDirection: "row",
  justifyContent: "center",
  marginTop: spacing.xl,
}

const $footerText: TextStyle = {
  color: colors.palette.neutral100,
}

const $link: TextStyle = {
  marginLeft: spacing.xs,
  textDecorationLine: "underline",
  color: colors.palette.neutral100,
}

const $forgotPassword: TextStyle = {
  textAlign: "center",
  marginTop: spacing.md,
  color: colors.palette.neutral100,
}

const $termsContainer: ViewStyle = {
  flexDirection: "row",
  flexWrap: "wrap",
  justifyContent: "center",
  marginTop: spacing.xl,
  paddingHorizontal: spacing.md,
}

const $termsText: TextStyle = {
  color: colors.palette.neutral100,
  textAlign: "center",
  fontSize: 12,
}

const $termsTextInner: TextStyle = {
  color: colors.palette.neutral100,
}

const $errorText: TextStyle = {
  color: colors.error,
  marginTop: spacing.xs,
  marginLeft: spacing.sm,
}

