"use client"

import { type FC, useState } from "react"
import { observer } from "mobx-react-lite"
import { type ViewStyle, View, type TextStyle, Image, type ImageStyle, TouchableOpacity, ActivityIndicator, Platform, KeyboardAvoidingView, ScrollView, useWindowDimensions } from "react-native"
import { Button, Screen, TextField, Text, GradientBackground, SuccessScreen } from "@/components"
import { colors, spacing } from "@/theme"
import { useStores } from "@/models"
import type { AppStackScreenProps } from "@/navigators"
import { globalButton, globalButtonText, globalBlueButton, globalBlueButtonText, globalTextInput } from "@/theme/globalStyles"
import { TxKeyPath } from "@/i18n"
import type { GeneralApiProblem } from "@/services/api/apiProblem"
import { ErrorScreen } from "@/components/ErrorScreen"

interface EmailVerificationScreenProps extends AppStackScreenProps<"EmailVerification"> {}

export const EmailVerificationScreen: FC<EmailVerificationScreenProps> = observer(
  function EmailVerificationScreen(_props) {
    const { userStore } = useStores()
    const { navigation } = _props
    const { width: windowWidth, height: windowHeight } = useWindowDimensions()

    // Calculate responsive sizes
    const logoSize = Math.min(windowWidth * 0.3, 150) // Cap logo size on large screens
    const formMaxWidth = Math.min(windowWidth * 0.9, 400) // Cap form width on large screens

    const [code, setCode] = useState("")
    const [isSubmitting, setIsSubmitting] = useState(false)
    const [isResending, setIsResending] = useState(false)
    const [isSuccessVisible, setIsSuccessVisible] = useState(false)
    const [isErrorVisible, setIsErrorVisible] = useState(false)
    const [errorMessage, setErrorMessage] = useState<TxKeyPath>("errorScreen:generalDescription" as TxKeyPath)

    const handleVerify = async () => {
      if (!code) {
        setErrorMessage("errorScreen:emailVerificationDescription" as TxKeyPath)
        setIsErrorVisible(true)
        return
      }

      setIsSubmitting(true)
      try {
        const result = await userStore.approveUser(code)
        if (result.kind === "ok") {
          setIsSuccessVisible(true)
        } else {
          // Handle specific error cases based on the error message
          if (result.error && typeof result.error === "string") {
            if (result.error.includes("already been used")) {
              setErrorMessage("errorScreen:codeUsedError" as TxKeyPath)
            } else if (result.error.includes("expired")) {
              setErrorMessage("errorScreen:codeExpiredError" as TxKeyPath)
            } else if (result.error.includes("not found")) {
              setErrorMessage("errorScreen:invalidCodeError" as TxKeyPath)
            } else {
              setErrorMessage("errorScreen:generalDescription" as TxKeyPath)
            }
          } else {
            setErrorMessage("errorScreen:generalDescription" as TxKeyPath)
          }
          setIsErrorVisible(true)
        }
      } finally {
        setIsSubmitting(false)
      }
    }

    const handleResend = async () => {
      setIsResending(true)
      try {
        const result = await userStore.resendVerificationCode()
        if (result.kind === "ok") {
          setErrorMessage("errorScreen:emailVerificationDescription" as TxKeyPath)
          setIsErrorVisible(true)
        }
      } finally {
        setIsResending(false)
      }
    }

    const handleSuccessClose = () => {
      setIsSuccessVisible(false)
      navigation.navigate("Login")
    }

    const handleErrorClose = () => {
      setIsErrorVisible(false)
    }

    return (
      <Screen
        preset="fixed"
        contentContainerStyle={$screenContentContainer}
        safeAreaEdges={["top", "bottom"]}
      >
        <GradientBackground />
        <KeyboardAvoidingView
          style={$keyboardAvoidingViewStyle}
          behavior={Platform.OS === "ios" ? "padding" : "height"}
          keyboardVerticalOffset={Platform.OS === "ios" ? 0 : 20}
        >
          <ScrollView
            style={$scrollViewStyle}
            contentContainerStyle={[
              $contentContainer,
              { minHeight: windowHeight * 0.9 },
            ]}
            keyboardShouldPersistTaps="handled"
            showsVerticalScrollIndicator={false}
            bounces={false}
          >
            <View style={[$mainContentContainer, { maxWidth: formMaxWidth, alignSelf: "center" }]}>
              <View style={$logoContainer}>
                <Image 
                  source={require("../../../assets/images/logo.png")} 
                  style={[$logo, { width: logoSize, height: logoSize }]} 
                  resizeMode="contain" 
                />
              </View>

              <Text tx="emailVerificationScreen:title" preset="subheading" style={$title} />
              <Text tx="emailVerificationScreen:description" style={$description} />

              <View style={$formContainer}>
                <TextField
                  value={code}
                  onChangeText={setCode}
                  containerStyle={$textField}
                  autoCapitalize="none"
                  autoCorrect={false}
                  labelTx="emailVerificationScreen:codeLabel"
                  placeholderTx="emailVerificationScreen:codePlaceholder"
                  inputWrapperStyle={globalTextInput}
                  editable={!isSubmitting}
                />

                <TouchableOpacity 
                  style={[globalButton, isSubmitting && { opacity: 0.7 }]} 
                  onPress={handleVerify}
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <ActivityIndicator color={colors.palette.neutral100} />
                  ) : (
                    <Text tx="emailVerificationScreen:verify" style={globalButtonText} />
                  )}
                </TouchableOpacity>

                <TouchableOpacity 
                  style={[globalBlueButton, isResending && { opacity: 0.7 }]} 
                  onPress={handleResend}
                  disabled={isResending}
                >
                  {isResending ? (
                    <ActivityIndicator color={colors.palette.neutral100} />
                  ) : (
                    <Text tx="emailVerificationScreen:resendCode" style={globalBlueButtonText} />
                  )}
                </TouchableOpacity>

                <View style={$termsContainer}>
                  <Text style={$termsText}>
                    <Text tx="resetPasswordScreen:termsText" style={$termsTextInner} />
                    {" "}
                    <Text 
                      tx="resetPasswordScreen:termsLink" 
                      style={[$termsTextInner, $link]}
                      onPress={() => console.log("Terms pressed")}
                    />
                    {" "}
                    <Text tx="resetPasswordScreen:andText" style={$termsTextInner} />
                    {" "}
                    <Text 
                      tx="resetPasswordScreen:privacyLink" 
                      style={[$termsTextInner, $link]}
                      onPress={() => console.log("Privacy pressed")}
                    />
                  </Text>
                </View>
              </View>
            </View>
          </ScrollView>
        </KeyboardAvoidingView>

        <SuccessScreen
          title="emailVerificationScreen:successTitle"
          message="emailVerificationScreen:successMessage"
          onClose={handleSuccessClose}
          visible={isSuccessVisible}
        />

        <ErrorScreen
          title="common:error"
          message={errorMessage}
          onClose={handleErrorClose}
          visible={isErrorVisible}
        />
      </Screen>
    )
  },
)

const $screenContentContainer: ViewStyle = {
  flex: 1,
}

const $keyboardAvoidingViewStyle: ViewStyle = {
  flex: 1,
}

const $scrollViewStyle: ViewStyle = {
  flex: 1,
}

const $contentContainer: ViewStyle = {
  flexGrow: 1,
  paddingHorizontal: spacing.lg,
  paddingVertical: spacing.xl,
  justifyContent: "center",
}

const $mainContentContainer: ViewStyle = {
  width: "100%",
  justifyContent: "center",
  paddingHorizontal: spacing.sm,
}

const $logoContainer: ViewStyle = {
  alignItems: "center",
  marginBottom: spacing.xxl,
}

const $formContainer: ViewStyle = {
  width: "100%",
}

const $logo: ImageStyle = {
  // Size is set dynamically
}

const $title: TextStyle = {
  color: colors.palette.neutral100,
  textAlign: "center",
  marginBottom: spacing.sm,
}

const $description: TextStyle = {
  marginBottom: spacing.xl,
  textAlign: "center",
  color: colors.palette.neutral100,
}

const $textField: ViewStyle = {
  marginBottom: spacing.lg,
}

const $termsContainer: ViewStyle = {
  flexDirection: "row",
  flexWrap: "wrap",
  justifyContent: "center",
  marginTop: spacing.xl,
  paddingHorizontal: spacing.md,
}

const $termsText: TextStyle = {
  color: colors.palette.neutral100,
  textAlign: "center",
  fontSize: 12,
}

const $termsTextInner: TextStyle = {
  color: colors.palette.neutral100,
}

const $link: TextStyle = {
  textDecorationLine: "underline",
  color: colors.palette.neutral100,
}

