import { FC } from "react"
import { observer } from "mobx-react-lite"
import { AppStackScreenProps } from "@/navigators"
import { Screen, Text } from "@/components"

interface SuccessScreenProps extends AppStackScreenProps<"Success"> {}

export const SuccessScreen: FC<SuccessScreenProps> = observer(function SuccessScreen({ 
  route,
  navigation,
}) {
  return (
    <Screen preset="auto">
      <Text 
        tx={`successScreen:${route.params.type}Title`}
        onPress={() => navigation.navigate("Login")} 
      />
    </Screen>
  )
}) 