import { View, Image } from "react-native"
import { colors, spacing, typography } from "../../theme"
import { Text } from "../../components"

export function Launch1() {
  return (
    <View style={{ flex: 1, justifyContent: "center", alignItems: "center", padding: spacing.lg }}>
      <Image
        source={require("../../../assets/images/launch1.png")}
        style={{
          width: "80%",
          height: "50%",
          resizeMode: "contain",
          marginBottom: spacing.xl,
        }}
      />
      <Text
        tx="onboarding:uploadPhotos.title"
        style={[typography.header, { textAlign: "center", marginBottom: spacing.md, color: colors.text }]}
      />
      <Text
        tx="onboarding:uploadPhotos.description"
        style={[typography.body, { textAlign: "center", color: colors.textDim }]}
      />
    </View>
  )
}

