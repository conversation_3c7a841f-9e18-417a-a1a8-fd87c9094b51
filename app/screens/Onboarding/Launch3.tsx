import { View, Image } from "react-native"
import { colors, spacing, typography } from "../../theme"
import { Text } from "../../components"

export function Launch3() {
  return (
    <View style={{ flex: 1, justifyContent: "center", alignItems: "center", padding: spacing.lg }}>
      <Image
        source={require("../../../assets/images/launch3.png")}
        style={{
          width: "80%",
          height: "50%",
          resizeMode: "contain",
          marginBottom: spacing.xxxl,
        }}
      />
      <Text
        tx="onboarding:viewResults.title"
        style={[typography.header, { textAlign: "center", marginBottom: spacing.md }]}
      />
      <Text
        tx="onboarding:viewResults.description"
        style={[typography.body, { textAlign: "center", color: colors.textDim }]}
      />
    </View>
  )
}

