"use client"

import { type FC, useState } from "react"
import { observer } from "mobx-react-lite"
import { type ViewStyle, View, type TextStyle, Image, type ImageStyle, Dimensions, TouchableOpacity, ActivityIndicator, Platform, KeyboardAvoidingView, ScrollView, useWindowDimensions, Keyboard } from "react-native"
import { Button, Screen, TextField, Text } from "@/components"
import { colors, spacing } from "@/theme"
import type { AppStackScreenProps } from "@/navigators"
import { GradientBackground } from "@/components/GradientBackground"
import { globalButton, globalButtonText, globalTextInput } from "@/theme/globalStyles"
import { ErrorScreen } from "@/components/ErrorScreen"
import { useStores } from "@/models"
import { TxKeyPath } from "@/i18n"

interface LoginScreenProps extends AppStackScreenProps<"Login"> {}

export const LoginScreen: FC<LoginScreenProps> = observer(function LoginScreen(_props) {
  const { navigation } = _props
  const { userStore } = useStores()
  const { width: windowWidth, height: windowHeight } = useWindowDimensions()

  // Calculate responsive sizes
  const logoSize = Math.min(windowWidth * 0.3, 150) // Cap logo size on large screens
  const formMaxWidth = Math.min(windowWidth * 0.9, 400) // Cap form width on large screens

  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [isPasswordVisible, setIsPasswordVisible] = useState(false)
  const [isAuthenticationError, setIsAuthenticationError] = useState(false)
  const [errorMessage, setErrorMessage] = useState<TxKeyPath>("loginScreen:loginError")
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleLogin = async () => {
    Keyboard.dismiss() // Prevent keyboard-related jumps
    if (!email || !password) {
      setErrorMessage("loginScreen:emptyFieldsError")
      setIsAuthenticationError(true)
      return
    }
  
    setIsSubmitting(true)
    try {
      const result = await userStore.login(email, password)
      if (result.kind === "ok") {
        setIsAuthenticationError(false)
      } else {
        setErrorMessage(
          result.error === "INVALID_CREDENTIALS" 
            ? "loginScreen:invalidCredentials"
            : result.error === "TOO_MANY_ATTEMPTS"
              ? "loginScreen:tooManyAttempts"
              : "loginScreen:loginError"
        )
        setIsAuthenticationError(true)
      }
    } catch (error) {
      console.error("Login error:", error)
      setErrorMessage("loginScreen:loginError")
      setIsAuthenticationError(true)
    } finally {
      setIsSubmitting(false)
    }
  }
  return (
    <Screen
      preset="fixed"
      contentContainerStyle={$screenContentContainer}
      safeAreaEdges={["top", "bottom"]}
    >
      <GradientBackground />
      <KeyboardAvoidingView
        style={$keyboardAvoidingViewStyle}
        behavior={Platform.OS === "ios" ? "padding" : "padding"} // Use padding on Android too
        keyboardVerticalOffset={Platform.OS === "ios" ? 60 : 40} // Adjust offset
      >
        <ScrollView
          style={$scrollViewStyle}
          contentContainerStyle={$contentContainer} // Remove minHeight
          keyboardShouldPersistTaps="handled" // Change to "handled" for better control
          showsVerticalScrollIndicator={false}
          bounces={false}
          keyboardDismissMode="on-drag" // Change to "on-drag" for smoother dismissal
        >
          <View style={[$mainContentContainer, { maxWidth: formMaxWidth, alignSelf: "center" }]}>
            <View style={$logoContainer}>
              <Image 
                source={require("../../../assets/images/logo.png")} 
                style={[$logo, { width: logoSize, height: logoSize }]} 
                resizeMode="contain" 
              />
            </View>

            <View style={$formContainer}>
              <TextField
                value={email}
                onChangeText={setEmail}
                containerStyle={$textField}
                autoCapitalize="none"
                autoComplete="email"
                autoCorrect={false}
                keyboardType="email-address"
                inputMode="email"
                labelTx="loginScreen:emailLabel"
                placeholderTx="loginScreen:emailPlaceholder"
                labelStyle={{ color: colors.palette.neutral100 }}
                status={isAuthenticationError ? "error" : undefined}
                inputWrapperStyle={globalTextInput}
                editable={!isSubmitting}
              />

              <TextField
                value={password}
                onChangeText={setPassword}
                containerStyle={$textField}
                autoCapitalize="none"
                autoComplete="password"
                autoCorrect={false}
                secureTextEntry={!isPasswordVisible}
                labelTx="loginScreen:passwordLabel"
                placeholderTx="loginScreen:passwordPlaceholder"
                status={isAuthenticationError ? "error" : undefined}
                RightAccessory={(props) => (
                  <Text
                    text={isPasswordVisible ? "👁" : "👁‍🗨"}
                    onPress={() => setIsPasswordVisible(!isPasswordVisible)}
                    style={[props.style, { color: colors.palette.neutral100 }]}
                  />
                )}
                inputWrapperStyle={globalTextInput}
                editable={!isSubmitting}
              />

              <TouchableOpacity 
                style={[globalButton, isSubmitting && { opacity: 0.7 }]} 
                onPress={handleLogin}
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <ActivityIndicator color={colors.palette.neutral100} />
                ) : (
                  <Text style={globalButtonText}>Login</Text>
                )}
              </TouchableOpacity>

              <Button
                tx="loginScreen:appleLogin"
                style={$socialButton}
                preset="default"
                disabled={isSubmitting}
                LeftAccessory={() => <View style={{ justifyContent: 'center', alignItems: 'center', marginRight: spacing.md }}>
                  <Image
                    source={require("../../../assets/images/apple-logo.png")}
                    style={{
                      width: 20,
                      height: 20,
                      resizeMode: "contain",
                    }}
                  />
                </View>}
              />

              <Button
                tx="loginScreen:googleLogin"
                style={$socialButton}
                preset="default"
                disabled={isSubmitting}
                LeftAccessory={() => <View style={{ justifyContent: 'center', alignItems: 'center', marginRight: spacing.md }}>
                  <Image
                    source={require("../../../assets/images/google-logo.png")}
                    style={{
                      width: 20,
                      height: 20,
                      resizeMode: "contain",
                    }}
                  />
                </View>}
              />
            </View>

            <View style={$footerContainer}>
              <Text
                tx="loginScreen:registerLink"
                preset="subheading"
                style={$footerText}
                onPress={() => navigation.navigate("Register")}
              />
              <Text
                tx="loginScreen:forgotPassword"
                preset="subheading"
                style={$footerText}
                onPress={() => navigation.navigate("ForgotPassword")}
              />
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>

      <ErrorScreen
        title="common:error"
        message={errorMessage}
        onClose={() => setIsAuthenticationError(false)}
        visible={isAuthenticationError}
      />
    </Screen>
  )
})

const $screenContentContainer: ViewStyle = {
  flex: 1,
  backgroundColor: colors.background,
}

const $keyboardAvoidingViewStyle: ViewStyle = {
  flex: 1,
  backgroundColor: 'transparent',
}

const $scrollViewStyle: ViewStyle = {
  flex: 1,
  backgroundColor: 'transparent',
}

const $contentContainer: ViewStyle = {
  flexGrow: 1,
  paddingHorizontal: spacing.lg,
  paddingVertical: spacing.xl,
  justifyContent: "center",
  alignItems: "center",
  backgroundColor: 'transparent',
}

const $mainContentContainer: ViewStyle = {
  width: "100%",
  justifyContent: "center",
  paddingHorizontal: spacing.sm,
  backgroundColor: 'transparent',
}

const $logoContainer: ViewStyle = {
  alignItems: "center",
  marginBottom: spacing.xxl,
}

const $formContainer: ViewStyle = {
  width: "100%",
}

const $logo: ImageStyle = {
  // Size is set dynamically
}

const $textField: ViewStyle = {
  marginBottom: spacing.lg,
}

const $socialButton: ViewStyle = {
  marginTop: spacing.md,
  backgroundColor: colors.background,
  borderWidth: 1,
  borderColor: colors.border,
  borderRadius: 11,
}

const $footerContainer: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  marginTop: spacing.xxl,
  width: "100%",
}

const $footerText: TextStyle = {
  color: colors.palette.neutral100,
}

