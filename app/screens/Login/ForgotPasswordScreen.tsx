"use client"

import { type FC, useState } from "react"
import { observer } from "mobx-react-lite"
import { type ViewStyle, View, type TextStyle, Image, type ImageStyle, TouchableOpacity, ActivityIndicator, Platform, KeyboardAvoidingView, ScrollView, useWindowDimensions } from "react-native"
import { Button, Screen, TextField, Text, GradientBackground, SuccessScreen } from "@/components"
import { ErrorScreen } from "@/components/ErrorScreen"
import { colors, spacing } from "@/theme"
import type { AppStackScreenProps } from "@/navigators"
import { globalButton, globalButtonText, globalTextInput } from "@/theme/globalStyles"
import { useStores } from "@/models"
import { TxKeyPath } from "@/i18n"

interface ForgotPasswordScreenProps extends AppStackScreenProps<"ForgotPassword"> {}

export const ForgotPasswordScreen: FC<ForgotPasswordScreenProps> = observer(function ForgotPasswordScreen(_props) {
  const { userStore } = useStores()
  const { navigation } = _props
  const { width: windowWidth, height: windowHeight } = useWindowDimensions()

  // Calculate responsive sizes
  const logoSize = Math.min(windowWidth * 0.3, 150) // Cap logo size on large screens
  const formMaxWidth = Math.min(windowWidth * 0.9, 400) // Cap form width on large screens

  const [email, setEmail] = useState("")
  const [isSuccessVisible, setIsSuccessVisible] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isErrorVisible, setIsErrorVisible] = useState(false)
  const [errorMessage, setErrorMessage] = useState<TxKeyPath>("errorScreen:generalDescription")

  const handleSubmit = async () => {
    if (!email) {
      setErrorMessage("forgotPasswordScreen:emailRequired" as TxKeyPath);
      setIsErrorVisible(true);
      return;
    }

    setIsSubmitting(true);
    try {
      const result = await userStore.forgotPassword(email);
      if (result.kind === "ok") {
        setIsSuccessVisible(true);
      } else {
        setErrorMessage(
          result.error === "TOO_MANY_ATTEMPTS"
            ? ("forgotPasswordScreen:tooManyAttempts" as TxKeyPath)
            : ("forgotPasswordScreen:generalError" as TxKeyPath)
        );
        setIsErrorVisible(true);
      }
    } catch (error) {
      console.error("Forgot password error:", error);
      setErrorMessage("forgotPasswordScreen:generalError" as TxKeyPath);
      setIsErrorVisible(true);
    } finally {
      setIsSubmitting(false);
    }
  }

  const handleSuccessClose = () => {
    setIsSuccessVisible(false)
    navigation.navigate("ResetPassword", { email })
  }

  return (
    <Screen
      preset="fixed"
      contentContainerStyle={$screenContentContainer}
      safeAreaEdges={["top", "bottom"]}
    >
      <GradientBackground />
      <KeyboardAvoidingView
        style={$keyboardAvoidingViewStyle}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        keyboardVerticalOffset={Platform.OS === "ios" ? 0 : 20}
      >
        <ScrollView
          style={$scrollViewStyle}
          contentContainerStyle={[
            $contentContainer,
            { minHeight: windowHeight * 0.9 },
          ]}
          keyboardShouldPersistTaps="always"
          showsVerticalScrollIndicator={false}
          bounces={false}
          keyboardDismissMode="none"
        >
          <View style={[$mainContentContainer, { maxWidth: formMaxWidth, alignSelf: "center" }]}>
            <View style={$logoContainer}>
              <Image 
                source={require("../../../assets/images/logo.png")} 
                style={[$logo, { width: logoSize, height: logoSize }]} 
                resizeMode="contain"
              />
            </View>

            <Text tx="forgotPasswordScreen:title" preset="subheading" style={$title} />
            <Text tx="forgotPasswordScreen:description" style={$description} />

            <View style={$formContainer}>
              <TextField
                value={email}
                onChangeText={setEmail}
                containerStyle={$textField}
                autoCapitalize="none"
                autoComplete="email"
                autoCorrect={false}
                keyboardType="email-address"
                labelTx="forgotPasswordScreen:emailLabel"
                placeholderTx="forgotPasswordScreen:emailPlaceholder"
                inputWrapperStyle={globalTextInput}
                editable={!isSubmitting}
              />

              <TouchableOpacity 
                style={[globalButton, isSubmitting && { opacity: 0.7 }]} 
                onPress={handleSubmit}
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <ActivityIndicator color={colors.palette.neutral100} />
                ) : (
                  <Text tx="forgotPasswordScreen:submit" style={globalButtonText} />
                )}
              </TouchableOpacity>

              <Text
                tx="forgotPasswordScreen:backToLogin"
                style={[$footerText, $link]}
                onPress={() => navigation.navigate("Login")}
              />
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>

      <SuccessScreen
        title="forgotPasswordScreen:successTitle"
        message="forgotPasswordScreen:successMessage"
        onClose={handleSuccessClose}
        visible={isSuccessVisible}
      />

      <ErrorScreen
        title="common:error"
        message={errorMessage}
        onClose={() => setIsErrorVisible(false)}
        visible={isErrorVisible}
      />
    </Screen>
  )
})

const $screenContentContainer: ViewStyle = {
  flex: 1,
}

const $keyboardAvoidingViewStyle: ViewStyle = {
  flex: 1,
}

const $scrollViewStyle: ViewStyle = {
  flex: 1,
}

const $contentContainer: ViewStyle = {
  flexGrow: 1,
  paddingHorizontal: spacing.lg,
  paddingVertical: spacing.xl,
  justifyContent: "center",
}

const $mainContentContainer: ViewStyle = {
  width: "100%",
  justifyContent: "center",
  paddingHorizontal: spacing.sm,
}

const $logoContainer: ViewStyle = {
  alignItems: "center",
  marginBottom: spacing.xxl,
}

const $formContainer: ViewStyle = {
  width: "100%",
}

const $logo: ImageStyle = {
  // Size is set dynamically
}

const $title: TextStyle = {
  color: colors.palette.neutral100,
  textAlign: "center",
  marginBottom: spacing.sm,
}

const $description: TextStyle = {
  marginBottom: spacing.xl,
  textAlign: "center",
  color: colors.palette.neutral100,
}

const $textField: ViewStyle = {
  marginBottom: spacing.lg,
}

const $footerText: TextStyle = {
  color: colors.palette.neutral100,
  textAlign: "center",
  marginTop: spacing.xl,
}

const $link: TextStyle = {
  textDecorationLine: "underline",
  color: colors.palette.neutral100,
}

