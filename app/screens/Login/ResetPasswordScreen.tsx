"use client"

import { type FC, useState } from "react"
import { observer } from "mobx-react-lite"
import { type ViewStyle, View, type TextStyle, type ImageStyle, Image, TouchableOpacity, ActivityIndicator, Platform, KeyboardAvoidingView, ScrollView, useWindowDimensions } from "react-native"
import { Button, Icon, Screen, TextField, Text, GradientBackground, SuccessScreen } from "@/components"
import { colors, spacing } from "@/theme"
import { useStores } from "@/models"
import type { AppStackScreenProps } from "@/navigators"
import { globalButton, globalButtonText, globalBlueButton, globalBlueButtonText, globalTextInput } from "@/theme/globalStyles"
import { ErrorScreen } from "@/components/ErrorScreen"
import { TxKeyPath } from "@/i18n"

interface ResetPasswordScreenProps extends AppStackScreenProps<"ResetPassword"> {}

export const ResetPasswordScreen: FC<ResetPasswordScreenProps> = observer(function ResetPasswordScreen(_props) {
  const { userStore } = useStores()
  const { navigation, route } = _props
  const { width: windowWidth, height: windowHeight } = useWindowDimensions()
  const email = route.params.email

  // Calculate responsive sizes
  const logoSize = Math.min(windowWidth * 0.3, 150)
  const formMaxWidth = Math.min(windowWidth * 0.9, 400)

  const [code, setCode] = useState("")
  const [password, setPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [isPasswordVisible, setIsPasswordVisible] = useState(false)
  const [isConfirmPasswordVisible, setIsConfirmPasswordVisible] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isResending, setIsResending] = useState(false)
  const [isSuccessVisible, setIsSuccessVisible] = useState(false)
  const [isResendSuccessVisible, setIsResendSuccessVisible] = useState(false)
  const [isErrorVisible, setIsErrorVisible] = useState(false)
  const [errorMessage, setErrorMessage] = useState<TxKeyPath>("errorScreen:generalDescription")

  const handleResend = async () => {
    setIsResending(true)
    try {
      const result = await userStore.forgotPassword(email)
      if (result.kind === "ok") {
        setIsResendSuccessVisible(true)
      } else {
        setErrorMessage(
          result.error === "TOO_MANY_ATTEMPTS"
            ? ("forgotPasswordScreen:tooManyAttempts" as TxKeyPath)
            : ("forgotPasswordScreen:generalError" as TxKeyPath)
        )
        setIsErrorVisible(true)
      }
    } catch (error) {
      console.error("Resend code error:", error)
      setErrorMessage("forgotPasswordScreen:generalError" as TxKeyPath)
      setIsErrorVisible(true)
    } finally {
      setIsResending(false)
    }
  }

  const handleReset = async () => {
    if (!code || !password || !confirmPassword) {
      setErrorMessage("resetPasswordScreen:allFieldsRequired" as TxKeyPath)
      setIsErrorVisible(true)
      return
    }

    if (password !== confirmPassword) {
      setErrorMessage("resetPasswordScreen:passwordsDoNotMatch" as TxKeyPath)
      setIsErrorVisible(true)
      return
    }

    setIsSubmitting(true)
    try {
      const result = await userStore.resetPassword(code, password, confirmPassword)
      if (result.kind === "ok") {
        setIsSuccessVisible(true)
      } else {
        setErrorMessage(
          result.error === "INVALID_CODE"
            ? ("errorScreen:invalidCodeError" as TxKeyPath)
            : result.error === "CODE_EXPIRED"
              ? ("errorScreen:codeExpiredError" as TxKeyPath)
              : ("resetPasswordScreen:resetError" as TxKeyPath)
        )
        setIsErrorVisible(true)
      }
    } catch (error) {
      console.error("Reset password error:", error)
      setErrorMessage("resetPasswordScreen:resetError" as TxKeyPath)
      setIsErrorVisible(true)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleSuccessClose = () => {
    setIsSuccessVisible(false)
    navigation.navigate("Login")
  }

  return (
    <Screen
      preset="fixed"
      contentContainerStyle={$screenContentContainer}
      safeAreaEdges={["top", "bottom"]}
    >
      <GradientBackground />
      <KeyboardAvoidingView
        style={$keyboardAvoidingViewStyle}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        keyboardVerticalOffset={Platform.OS === "ios" ? 0 : 20}
      >
        <ScrollView
          style={$scrollViewStyle}
          contentContainerStyle={[
            $contentContainer,
            { minHeight: windowHeight * 0.9 },
          ]}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
          bounces={false}
        >
          <View style={[$mainContentContainer, { maxWidth: formMaxWidth, alignSelf: "center" }]}>
            <View style={$logoContainer}>
              <Image 
                source={require("../../../assets/images/logo.png")} 
                style={[$logo, { width: logoSize, height: logoSize }]} 
                resizeMode="contain" 
              />
            </View>

            <Text tx="resetPasswordScreen:title" preset="subheading" style={$title} />
            <Text tx="resetPasswordScreen:description" style={$description} />

            <View style={$formContainer}>
              <TextField
                value={code}
                onChangeText={setCode}
                containerStyle={$textField}
                autoCapitalize="none"
                autoCorrect={false}
                labelTx="resetPasswordScreen:codeLabel"
                placeholderTx="resetPasswordScreen:codePlaceholder"
                inputWrapperStyle={globalTextInput}
                editable={!isSubmitting}
              />

              <TextField
                value={password}
                onChangeText={setPassword}
                containerStyle={$textField}
                autoCapitalize="none"
                autoComplete="password"
                autoCorrect={false}
                secureTextEntry={!isPasswordVisible}
                labelTx="resetPasswordScreen:passwordLabel"
                placeholderTx="resetPasswordScreen:passwordPlaceholder"
                RightAccessory={(props) => (
                  <Icon
                    icon={isPasswordVisible ? "view" : "hidden"}
                    color={colors.text}
                    containerStyle={props.style}
                    size={20}
                    onPress={() => setIsPasswordVisible(!isPasswordVisible)}
                  />
                )}
                inputWrapperStyle={globalTextInput}
                editable={!isSubmitting}
              />

              <TextField
                value={confirmPassword}
                onChangeText={setConfirmPassword}
                containerStyle={$textField}
                autoCapitalize="none"
                autoComplete="password"
                autoCorrect={false}
                secureTextEntry={!isConfirmPasswordVisible}
                labelTx="resetPasswordScreen:confirmPasswordLabel"
                placeholderTx="resetPasswordScreen:confirmPasswordPlaceholder"
                RightAccessory={(props) => (
                  <Icon
                    icon={isConfirmPasswordVisible ? "view" : "hidden"}
                    color={colors.text}
                    containerStyle={props.style}
                    size={20}
                    onPress={() => setIsConfirmPasswordVisible(!isConfirmPasswordVisible)}
                  />
                )}
                inputWrapperStyle={globalTextInput}
                editable={!isSubmitting}
              />

              <TouchableOpacity 
                style={[globalButton, isSubmitting && { opacity: 0.7 }]} 
                onPress={handleReset}
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <ActivityIndicator color={colors.palette.neutral100} />
                ) : (
                  <Text tx="resetPasswordScreen:submit" style={globalButtonText} />
                )}
              </TouchableOpacity>

              <TouchableOpacity 
                style={[globalButton, { marginTop: spacing.md }, isResending && { opacity: 0.7 }]} 
                onPress={handleResend}
                disabled={isResending}
              >
                {isResending ? (
                  <ActivityIndicator color={colors.palette.neutral100} />
                ) : (
                  <Text tx="resetPasswordScreen:resendCode" style={globalButtonText} />
                )}
              </TouchableOpacity>

              <View style={$termsContainer}>
                <Text style={$termsText}>
                  <Text tx="resetPasswordScreen:termsText" style={$termsTextInner} />
                  {" "}
                  <Text 
                    tx="resetPasswordScreen:termsLink" 
                    style={[$termsTextInner, $link]}
                    onPress={() => console.log("Terms pressed")}
                  />
                  {" "}
                  <Text tx="resetPasswordScreen:andText" style={$termsTextInner} />
                  {" "}
                  <Text 
                    tx="resetPasswordScreen:privacyLink" 
                    style={[$termsTextInner, $link]}
                    onPress={() => console.log("Privacy pressed")}
                  />
                </Text>
              </View>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>

      <SuccessScreen
        title="successScreen:passwordReset.title"
        message="successScreen:passwordReset.message"
        onClose={handleSuccessClose}
        visible={isSuccessVisible}
      />

      <SuccessScreen
        title="forgotPasswordScreen:successTitle"
        message="forgotPasswordScreen:codeSent"
        onClose={() => setIsResendSuccessVisible(false)}
        visible={isResendSuccessVisible}
      />

      <ErrorScreen
        title="common:error"
        message={errorMessage}
        onClose={() => setIsErrorVisible(false)}
        visible={isErrorVisible}
      />
    </Screen>
  )
})

const $screenContentContainer: ViewStyle = {
  flex: 1,
}

const $keyboardAvoidingViewStyle: ViewStyle = {
  flex: 1,
}

const $scrollViewStyle: ViewStyle = {
  flex: 1,
}

const $contentContainer: ViewStyle = {
  flexGrow: 1,
  paddingHorizontal: spacing.lg,
  paddingVertical: spacing.xl,
  justifyContent: "center",
}

const $mainContentContainer: ViewStyle = {
  width: "100%",
  justifyContent: "center",
  paddingHorizontal: spacing.sm,
}

const $logoContainer: ViewStyle = {
  alignItems: "center",
  marginBottom: spacing.xxl,
}

const $formContainer: ViewStyle = {
  width: "100%",
}

const $logo: ImageStyle = {
  // Size is set dynamically
}

const $title: TextStyle = {
  color: colors.palette.neutral100,
  textAlign: "center",
  marginBottom: spacing.sm,
}

const $description: TextStyle = {
  marginBottom: spacing.xl,
  textAlign: "center",
  color: colors.palette.neutral100,
}

const $textField: ViewStyle = {
  marginBottom: spacing.lg,
}

const $termsContainer: ViewStyle = {
  flexDirection: "row",
  flexWrap: "wrap",
  justifyContent: "center",
  marginTop: spacing.xl,
  paddingHorizontal: spacing.md,
}

const $termsText: TextStyle = {
  color: colors.palette.neutral100,
  textAlign: "center",
  fontSize: 12,
}

const $termsTextInner: TextStyle = {
  color: colors.palette.neutral100,
}

const $link: TextStyle = {
  textDecorationLine: "underline",
  color: colors.palette.neutral100,
}

