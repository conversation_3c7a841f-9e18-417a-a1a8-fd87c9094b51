import type { FC } from "react"
import { observer } from "mobx-react-lite"
import { type ViewStyle, View, type TextStyle, Image, type ImageStyle, TouchableOpacity, RefreshControl, ScrollView } from "react-native"
import { Screen, Text, Header } from "@/components"
import { colors, spacing } from "@/theme"
import { useStores } from "@/models"
import type { AppStackScreenProps } from "@/navigators"
import { MaterialIcons } from '@expo/vector-icons'
import { TxKeyPath } from "@/i18n"
import { useState, useCallback } from "react"

interface ProfileScreenProps extends AppStackScreenProps<"Profile"> {}

interface InfoItemProps {
  label: TxKeyPath
  value?: string
}

export const ProfileScreen: FC<ProfileScreenProps> = observer(function ProfileScreen(_props) {
  const { userStore } = useStores()
  const { navigation } = _props
  const user = userStore.currentUser
  const [refreshing, setRefreshing] = useState(false)

  const onRefresh = useCallback(async () => {
    setRefreshing(true)
    try {
      await userStore.refreshProfile()
    } finally {
      setRefreshing(false)
    }
  }, [userStore])

  const handleEditProfile = () => {
    navigation.navigate("EditProfile")
  }

  const handleChangePhoto = () => {
    navigation.navigate("ChoosePhoto")
  }

  if (!user) return null

  return (
    <Screen safeAreaEdges={["top"]}>
      <Header
        // title={`${user?.firstName} ${user?.lastName}`}
        // titleMode="center"
        RightActionComponent={
          <View style={$rightContainer}>
            <TouchableOpacity onPress={handleEditProfile}>
              <Text tx="common:edit" />
            </TouchableOpacity>
          </View>
        }
      />

      <ScrollView 
        contentContainerStyle={$screenContentContainer}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor={colors.text}
          />
        }
      >
        <View style={$profileContainer}>
          <TouchableOpacity onPress={handleChangePhoto} style={$imageContainer}>
            <Image
              source={user.avatar ? { uri: user.avatar } : require("../../../assets/images/logo.png")}
              style={$profileImage}
            />
            <View style={$editIconContainer}>
              <MaterialIcons name="camera-alt" size={20} color={colors.palette.neutral100} />
            </View>
          </TouchableOpacity>

          <Text text={`${user.name || ''} ${user.surname || ''}`} preset="heading" style={$name} />
          <Text text={user.email} preset="formLabel" style={$email} />
        </View>

        <View style={$infoContainer}>
          <InfoItem label="profileScreen:emailLabel" value={user.email} />
          <InfoItem label="profileScreen:firstNameLabel" value={user.name} />
          <InfoItem label="profileScreen:lastNameLabel" value={user.surname} />
        </View>
      </ScrollView>
    </Screen>
  )
})

const InfoItem = ({ label, value }: InfoItemProps) => (
  <View style={$infoItem}>
    <Text tx={label} preset="formLabel" style={$infoLabel} />
    <Text text={value} style={$infoValue} />
  </View>
)

const $screenContentContainer: ViewStyle = {
  paddingBottom: spacing.xxl,
}

const $profileContainer: ViewStyle = {
  alignItems: "center",
  paddingHorizontal: spacing.lg,
  paddingTop: spacing.xl,
  paddingBottom: spacing.xxl,
}

const $imageContainer: ViewStyle = {
  position: "relative",
  marginBottom: spacing.lg,
}

const $profileImage: ImageStyle = {
  width: 120,
  height: 120,
  borderRadius: 60,
}

const $editIconContainer: ViewStyle = {
  position: "absolute",
  right: 0,
  bottom: 0,
  backgroundColor: colors.palette.primary500,
  borderRadius: 20,
  padding: spacing.xs,
  borderWidth: 2,
  borderColor: colors.palette.neutral100,
}

const $name: TextStyle = {
  fontSize: 24,
  marginBottom: spacing.xs,
}

const $email: TextStyle = {
  color: colors.textDim,
}

const $infoContainer: ViewStyle = {
  paddingHorizontal: spacing.lg,
}

const $infoItem: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  paddingVertical: spacing.sm,
  borderBottomWidth: 1,
  borderBottomColor: colors.separator,
}

const $infoLabel: TextStyle = {
  color: colors.textDim,
}

const $infoValue: TextStyle = {
  color: colors.text,
}

const $rightContainer: ViewStyle = {
  flex: 1,
  alignItems: 'flex-end',
  paddingRight: spacing.md,
}

