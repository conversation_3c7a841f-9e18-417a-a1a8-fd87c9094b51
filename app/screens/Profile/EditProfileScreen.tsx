"use client"

import type { <PERSON> } from "react"
import { useState } from "react"
import { observer } from "mobx-react-lite"
import { type ViewStyle, View, type TextStyle, Image, type ImageStyle, TouchableOpacity, ActivityIndicator } from "react-native"
import { Button, Screen, TextField, Text, Header } from "@/components"
import { colors, spacing } from "@/theme"
import { globalGreenButton, globalGreenButtonText } from "@/theme/globalStyles"
import { useStores } from "@/models"
import type { AppStackScreenProps } from "@/navigators"
import { MaterialIcons } from '@expo/vector-icons'
import type { TxKeyPath } from "@/i18n"

interface EditProfileScreenProps extends AppStackScreenProps<"EditProfile"> {}

export const EditProfileScreen: FC<EditProfileScreenProps> = observer(function EditProfileScreen(_props) {
  const { userStore } = useStores()
  const { navigation } = _props
  const user = userStore.currentUser

  const [name, setName] = useState(user?.name || "")
  const [surname, setSurname] = useState(user?.surname || "")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<TxKeyPath | null>(null)

  const handleSave = async () => {
    // Clear previous error
    setError(null)

    // Validate required fields
    if (!name.trim() || !surname.trim()) {
      setError("errors:requiredFields")
      return
    }

    setIsSubmitting(true)
    try {
      const result = await userStore.updateProfile({
        name: name.trim(),
        surname: surname.trim(),
      })
      if (result.kind === "ok") {
        navigation.goBack()
      } else {
        setError("errors:updateProfileFailed")
      }
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleChangePhoto = () => {
    navigation.navigate("ChoosePhoto")
  }

  return (
    <Screen preset="scroll" contentContainerStyle={$screenContentContainer} safeAreaEdges={["top"]}>
      <Header 
        RightActionComponent={
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Text tx="common:backWithArrow" style={$backText} />
          </TouchableOpacity>
        }
        titleTx="profileScreen:editTitle"
        titleMode="center"
      />

      <View style={$profileContainer}>
        <TouchableOpacity onPress={handleChangePhoto} style={$imageContainer}>
          <Image
            source={user?.avatar ? { uri: user.avatar } : require("../../../assets/images/logo.png")}
            style={$profileImage}
          />
          <View style={$editIconContainer}>
            <MaterialIcons name="camera-alt" size={20} color={colors.palette.neutral100} />
          </View>
        </TouchableOpacity>
      </View>

      <View style={$formContainer}>
        <TextField
          value={name}
          onChangeText={setName}
          containerStyle={$textField}
          autoCapitalize="words"
          autoCorrect={false}
          labelTx="profileScreen:firstNameLabel"
          placeholderTx="profileScreen:firstNamePlaceholder"
          editable={!isSubmitting}
          status={!name.trim() ? "error" : undefined}
          helperTx={!name.trim() ? "errors:requiredField" : undefined}
        />

        <TextField
          value={surname}
          onChangeText={setSurname}
          containerStyle={$textField}
          autoCapitalize="words"
          autoCorrect={false}
          labelTx="profileScreen:lastNameLabel"
          placeholderTx="profileScreen:lastNamePlaceholder"
          editable={!isSubmitting}
          status={!surname.trim() ? "error" : undefined}
          helperTx={!surname.trim() ? "errors:requiredField" : undefined}
        />

        <Button 
          tx="common:save" 
          style={[globalGreenButton, $button]}
          textStyle={globalGreenButtonText}
          onPress={handleSave}
          disabled={isSubmitting || !name.trim() || !surname.trim()} 
          RightAccessory={isSubmitting ? () => <ActivityIndicator color={colors.palette.neutral100} /> : undefined}
        />

        {error && (
          <Text
            tx={error}
            style={$errorText}
          />
        )}
      </View>
    </Screen>
  )
})

const $screenContentContainer: ViewStyle = {
  paddingBottom: spacing.xxl,
}

const $profileContainer: ViewStyle = {
  alignItems: "center",
  paddingHorizontal: spacing.lg,
  paddingTop: spacing.xl,
  paddingBottom: spacing.xxl,
}

const $imageContainer: ViewStyle = {
  position: "relative",
  marginBottom: spacing.lg,
}

const $profileImage: ImageStyle = {
  width: 120,
  height: 120,
  borderRadius: 60,
}

const $editIconContainer: ViewStyle = {
  position: "absolute",
  right: 0,
  bottom: 0,
  backgroundColor: colors.palette.primary500,
  borderRadius: 20,
  padding: spacing.xs,
  borderWidth: 2,
  borderColor: colors.palette.neutral100,
}

const $editIcon: TextStyle = {
  fontSize: 20,
}

const $formContainer: ViewStyle = {
  paddingHorizontal: spacing.lg,
}

const $textField: ViewStyle = {
  marginBottom: spacing.lg,
}

const $button: ViewStyle = {
  marginTop: spacing.md,
}

const $backText: TextStyle = {
  color: colors.text,
  fontSize: 16,
  paddingHorizontal: spacing.md,
}

const $errorText: TextStyle = {
  color: colors.error,
  textAlign: "center",
  marginTop: spacing.sm,
}

