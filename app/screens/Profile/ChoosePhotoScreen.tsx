import type { FC } from "react"
import { observer } from "mobx-react-lite"
import { type ViewStyle, View, type TextStyle, Image, type ImageStyle, TouchableOpacity } from "react-native"
import { Button, Screen, Text, Header } from "@/components"
import { colors, spacing } from "@/theme"
import { globalRedButton, globalRedButtonText } from "@/theme/globalStyles"
import { useStores } from "@/models"
import type { AppStackScreenProps } from "@/navigators"
import * as ImagePicker from "expo-image-picker"
import { MaterialIcons } from '@expo/vector-icons'

interface ChoosePhotoScreenProps extends AppStackScreenProps<"ChoosePhoto"> {}

export const ChoosePhotoScreen: FC<ChoosePhotoScreenProps> = observer(function ChoosePhotoScreen(_props) {
  const { userStore } = useStores()
  const { navigation } = _props

  const handleTakePhoto = async () => {
    const permissionResult = await ImagePicker.requestCameraPermissionsAsync()

    if (permissionResult.granted === false) {
      return
    }

    const result = await ImagePicker.launchCameraAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.5,
    })

    if (!result.canceled) {
      const updateResult = await userStore.updateProfilePhoto(result.assets[0].uri)
      if (updateResult.kind === "ok") {
        await userStore.refreshProfile()
        navigation.goBack()
      }
    }
  }

  const handleChoosePhoto = async () => {
    const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync()

    if (permissionResult.granted === false) {
      return
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.5,
    })

    if (!result.canceled) {
      const updateResult = await userStore.updateProfilePhoto(result.assets[0].uri)
      if (updateResult.kind === "ok") {
        await userStore.refreshProfile()
        navigation.goBack()
      }
    }
  }

  return (
    <Screen preset="fixed" contentContainerStyle={$screenContentContainer} safeAreaEdges={["top", "bottom"]}>
      <Header 
        leftIcon="back" 
        onLeftPress={() => navigation.goBack()} 
        titleTx="choosePhotoScreen:title" 
      />

      <View style={$optionsContainer}>
        <View style={$optionsRow}>
          <TouchableOpacity style={$option} onPress={handleTakePhoto} activeOpacity={0.7}>
            <View style={$iconContainer}>
              <MaterialIcons name="camera-alt" size={24} color={colors.text} />
            </View>
            <Text tx="choosePhotoScreen:takePhoto" style={$optionText} />
          </TouchableOpacity>

          <TouchableOpacity style={$option} onPress={handleChoosePhoto} activeOpacity={0.7}>
            <View style={$iconContainer}>
              <MaterialIcons name="photo-library" size={24} color={colors.text} />
            </View>
            <Text tx="choosePhotoScreen:chooseFromGallery" style={$optionText} />
          </TouchableOpacity>
        </View>
      </View>

      <Button 
        tx="common:cancel" 
        style={[globalRedButton, $cancelButton]} 
        textStyle={globalRedButtonText}
        onPress={() => navigation.goBack()} 
      />
    </Screen>
  )
})

const $screenContentContainer: ViewStyle = {
  flex: 1,
  padding: spacing.lg,
  backgroundColor: colors.background,
}

const $logoContainer: ViewStyle = {
  alignItems: "center",
  marginVertical: spacing.xxl,
}

const $logo: ImageStyle = {
  width: 120,
  height: 120,
}

const $optionsContainer: ViewStyle = {
  flex: 1,
  justifyContent: "center",
}

const $optionsRow: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  gap: spacing.md,
}

const $option: ViewStyle = {
  flex: 1,
  flexDirection: "column",
  alignItems: "center",
  padding: spacing.md,
  backgroundColor: colors.palette.neutral100,
  borderRadius: spacing.sm,
  borderWidth: 1,
  borderColor: colors.palette.neutral300,
  height: 120,
  justifyContent: "center",
}

const $iconContainer: ViewStyle = {
  width: 48,
  height: 48,
  justifyContent: "center",
  alignItems: "center",
  backgroundColor: colors.palette.neutral200,
  borderRadius: spacing.sm,
  marginBottom: spacing.sm,
}

const $optionText: TextStyle = {
  fontSize: 16,
  color: colors.text,
  textAlign: "center",
  paddingHorizontal: spacing.xs,
}

const $cancelButton: ViewStyle = {
  marginTop: spacing.xl,
}

