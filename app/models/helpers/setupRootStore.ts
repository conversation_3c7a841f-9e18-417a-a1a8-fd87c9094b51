/**
 * This file is where we do "rehydration" of your RootStore from AsyncStorage.
 * This lets you persist your state between app launches.
 *
 * Navigation state persistence is handled in navigationUtilities.tsx.
 *
 * Note that Fast Refresh doesn't play well with this file, so if you edit this,
 * do a full refresh of your app instead.
 *
 * @refresh reset
 */
import { applySnapshot, IDisposer, onSnapshot } from "mobx-state-tree"
import { RootStore, RootStoreSnapshot } from "../RootStore"
import * as storage from "../../utils/storage"
import { navigate } from "../../navigators"
import { api } from "../../services/api/api"
/**
 * The key we'll be saving our state as within async storage.
 */
const ROOT_STATE_STORAGE_KEY = "root-v1"

const log = (message: string, data?: any) => {
  console.log(`[RootStore] ${message}`, data || '')
}

/**
 * Setup the root state.
 */
let _disposer: IDisposer | undefined

const EMPTY_STATE: RootStoreSnapshot = {
  authenticationStore: {
    authEmail: "",
    authToken: undefined,
  },
  userStore: {
    currentUser: undefined,
    isLoading: false,
    accessToken: undefined,
    refreshToken: undefined,
    isValidatingToken: false,
  },
}

const migrateState = (oldState: any): RootStoreSnapshot => {
  log("Migrating old state:", oldState)
  
  // Start with empty state
  const newState = { ...EMPTY_STATE }
  
  // Migrate user data if exists
  if (oldState?.userStore?.currentUser) {
    const oldUser = oldState.userStore.currentUser
    newState.userStore = {
      ...EMPTY_STATE.userStore,
      currentUser: {
        id: String(oldUser.id),
        email: oldUser.email,
        name: oldUser.firstName || oldUser.name,
        surname: oldUser.lastName || oldUser.surname,
        userType: oldUser.userType || "B",
        isApproved: oldUser.isApproved || false,
        state: oldUser.state || "pending",
        avatar: oldUser.avatar,
        createdAt: oldUser.createdAt,
        updatedAt: oldUser.updatedAt,
      },
      accessToken: oldState.userStore.accessToken,
      refreshToken: oldState.userStore.refreshToken,
    }
  }
  
  log("Migrated to new state:", newState)
  return newState as RootStoreSnapshot
}

const isValidState = (state: any): boolean => {
  if (!state?.userStore) return false
  
  const user = state.userStore.currentUser
  if (!user) return false
  
  // Check required fields
  if (!user.id || !user.email) {
    log("Invalid state: missing required user fields")
    return false
  }
  
  // Check tokens
  if (!state.userStore.accessToken || !state.userStore.refreshToken) {
    log("Invalid state: missing auth tokens")
    return false
  }
  
  return true
}

export async function setupRootStore(rootStore: RootStore) {
  let restoredState: RootStoreSnapshot | undefined | null
  let isAuthenticated = false

  try {
    log("Loading persisted state...")
    const loadedState = await storage.load(ROOT_STATE_STORAGE_KEY)
    log("Loaded raw state:", loadedState)
    
    if (loadedState) {
      restoredState = migrateState(loadedState)
      if (!isValidState(restoredState)) {
        log("Invalid state after migration, using empty state")
        restoredState = EMPTY_STATE
        await storage.remove(ROOT_STATE_STORAGE_KEY)
      }
    } else {
      restoredState = EMPTY_STATE
    }
    
    log("Applying snapshot...")
    applySnapshot(rootStore, restoredState)

    // Sync Api with UserStore tokens after snapshot
    if (rootStore.userStore.accessToken && rootStore.userStore.refreshToken) {
      api.setAuthTokens(rootStore.userStore.accessToken, rootStore.userStore.refreshToken)
      log("Synced Api with UserStore tokens:", rootStore.userStore.accessToken)
    }

    log("Validating authentication...")
    isAuthenticated = await rootStore.userStore.loadPersistedData()
    log("Authentication status:", isAuthenticated)
    
    if (!isAuthenticated) {
      log("User not authenticated, redirecting to login")
      setTimeout(() => navigate("Login"), 0)
    } else {
      log("User is authenticated, current user:", rootStore.userStore.currentUser)
    }
  } catch (e) {
    const error = e instanceof Error ? e : new Error(String(e))
    log("Setup error:", error.message)
    restoredState = EMPTY_STATE
    applySnapshot(rootStore, restoredState)
    setTimeout(() => navigate("Login"), 0)
  }

  if (_disposer) {
    log("Cleaning up previous disposer")
    _disposer()
  }

  _disposer = onSnapshot(rootStore, (snapshot) => {
    try {
      log("Saving state snapshot...")
      storage.save(ROOT_STATE_STORAGE_KEY, snapshot)
    } catch (saveError) {
      log("Failed to save state:", saveError)
    }
  })

  const unsubscribe = () => {
    if (_disposer) {
      log("Unsubscribing from state changes")
      _disposer()
      _disposer = undefined
    }
  }

  return { rootStore, restoredState, unsubscribe, isAuthenticated }
}