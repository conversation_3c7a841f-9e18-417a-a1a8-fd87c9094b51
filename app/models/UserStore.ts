import { Instance, SnapshotIn, SnapshotOut, types } from "mobx-state-tree"
import { withSetPropAction } from "./helpers/withSetPropAction"
import { api } from "../services/api/api"
import { CreateUserRequest } from "../services/api/api.types"
import AsyncStorage from "@react-native-async-storage/async-storage"

const STORAGE_KEYS = {
  AUTH_TOKENS: "auth_tokens",
  USER: "user",
} as const

export const UserModel = types
  .model("User")
  .props({
    id: types.string,
    email: types.string,
    name: types.maybe(types.string),
    surname: types.maybe(types.string),
    userType: types.optional(types.enumeration(["A", "B"]), "B"),
    isApproved: types.optional(types.boolean, false),
    createdAt: types.maybe(types.string),
    updatedAt: types.maybe(types.string),
    avatar: types.maybe(types.string),
    state: types.optional(types.enumeration(["pending", "approved", "banned"]), "pending"),
  })
  .actions(withSetPropAction)
  .views((self) => ({
    get fullName() {
      return [self.name, self.surname].filter(Boolean).join(" ")
    },
  }))

export const UserStoreModel = (() => {
  const model = types
    .model("UserStore")
    .props({
      currentUser: types.maybe(UserModel),
      isLoading: false,
      accessToken: types.maybe(types.string),
      refreshToken: types.maybe(types.string),
      isValidatingToken: false,
    })
    .actions(withSetPropAction)
    .actions((self) => ({
      setAuthTokens(accessToken: string, refreshToken: string) {
        self.setProp("accessToken", accessToken)
        self.setProp("refreshToken", refreshToken)
        api.setAuthTokens(accessToken, refreshToken)
        // Persist tokens
        AsyncStorage.setItem(
          STORAGE_KEYS.AUTH_TOKENS,
          JSON.stringify({ accessToken, refreshToken }),
        ).catch(error => console.error("Failed to persist tokens:", error))
      },

      async validateToken() {
        if (!self.accessToken) return false

        self.setProp("isValidatingToken", true)
        try {
          const result = await api.getProfile()
          if (result.kind === "ok") {
            self.setProp("currentUser", {
              ...result.user,
              id: String(result.user.id),
            })
            return true
          }
          
          // Token is invalid, try to refresh
          const refreshResult = await (self as any).refreshAuthToken()
          if (refreshResult) {
            // Retry validation with new token
            const retryResult = await api.getProfile()
            if (retryResult.kind === "ok") {
              self.setProp("currentUser", {
                ...retryResult.user,
                id: String(retryResult.user.id),
              })
              return true
            }
          }
          
          return false
        } catch (error) {
          console.error("Failed to validate token:", error)
          return false
        } finally {
          self.setProp("isValidatingToken", false)
        }
      },

      async loadPersistedData() {
        try {
          const [tokensString, userString] = await Promise.all([
            AsyncStorage.getItem(STORAGE_KEYS.AUTH_TOKENS),
            AsyncStorage.getItem(STORAGE_KEYS.USER),
          ])

          if (tokensString) {
            const { accessToken, refreshToken } = JSON.parse(tokensString)
            if (accessToken && refreshToken) {
              // Use API client's setAuthTokens to ensure headers are set
              api.setAuthTokens(accessToken, refreshToken)
              self.setProp("accessToken", accessToken)
              self.setProp("refreshToken", refreshToken)
              
              // Validate token with server
              const isValid = await (self as any).validateToken()
              if (!isValid) {
                // If token validation fails, clear everything
                (self as any).logout()
                return false
              }
              return true
            }
          }

          if (userString) {
            const user = JSON.parse(userString)
            self.setProp("currentUser", user)
          }
          
          return !!self.currentUser
        } catch (error) {
          console.error("Failed to load persisted data:", error)
          return false
        }
      },

      async refreshAuthToken() {
        if (!self.refreshToken) return false

        try {
          const result = await api.refreshToken(self.refreshToken)
          if (result.kind === "ok") {
            // First set the tokens in the API client to ensure headers are set
            api.setAuthTokens(result.tokens.accessToken, result.tokens.refreshToken)
            
            // Then update store tokens
            self.setProp("accessToken", result.tokens.accessToken)
            self.setProp("refreshToken", result.tokens.refreshToken)
            
            // Persist tokens
            await AsyncStorage.setItem(
              STORAGE_KEYS.AUTH_TOKENS,
              JSON.stringify({ 
                accessToken: result.tokens.accessToken, 
                refreshToken: result.tokens.refreshToken 
              })
            )
            
            return true
          }
          return false
        } catch (error) {
          console.error("Failed to refresh token:", error)
          // Clear tokens on refresh failure
          ;(self as any).logout()
          return false
        }
      },
      
      logout() {
        self.setProp("currentUser", undefined)
        self.setProp("accessToken", undefined)
        self.setProp("refreshToken", undefined)
        api.apisauce.deleteHeader("Authorization")
        // Clear persisted data
        Promise.all([
          AsyncStorage.removeItem(STORAGE_KEYS.AUTH_TOKENS),
          AsyncStorage.removeItem(STORAGE_KEYS.USER),
        ]).catch(error => console.error("Failed to clear persisted data:", error))
      },

      async register(userData: CreateUserRequest) {
        self.setProp("isLoading", true)
        try {
          const result = await api.register(userData)
          if (result.kind === "ok") {
            // Transform the user data to match our MST model
            const mstUser = {
              id: String(result.user.id), // MST model expects string
              email: result.user.email,
              name: result.user.name,
              surname: result.user.surname,
              userType: result.user.userType,
              isApproved: result.user.isApproved,
              createdAt: result.user.createdAt,
              updatedAt: result.user.updatedAt,
              state: "pending", // New users start as pending
            }
            self.setProp("currentUser", mstUser)
            return { kind: "ok" }
          } else {
            console.log("Registration result:", result)
            // Handle the error structure from the API
            if (result.kind === "rejected" && result.error) {
              return { kind: "error", error: result.error.message || result.error }
            }
            return { kind: "error", error: result.kind }
          }
        } catch (error) {
          console.error("Failed to register:", error)
          return { kind: "error", error: "unknown" }
        } finally {
          self.setProp("isLoading", false)
        }
      },

      async login(email: string, password: string) {
        self.setProp("isLoading", true)
        try {
          const result = await api.login({ email, password })
          if (result.kind === "ok" && result.user && result.tokens) {
            // Transform null values to undefined
            const transformedUser = {
              ...result.user,
              name: result.user.name || undefined,
              surname: result.user.surname || undefined,
              avatar: result.user.avatar || undefined,
              id: String(result.user.id), // Ensure id is a string
            }
            
            // First set the tokens in the API client to ensure headers are set
            api.setAuthTokens(result.tokens.accessToken, result.tokens.refreshToken)
            
            // Then set store tokens
            self.setProp("accessToken", result.tokens.accessToken)
            self.setProp("refreshToken", result.tokens.refreshToken)
            
            // Finally set the user
            self.setProp("currentUser", transformedUser)

            // Persist data
            await Promise.all([
              AsyncStorage.setItem(
                STORAGE_KEYS.AUTH_TOKENS,
                JSON.stringify({ 
                  accessToken: result.tokens.accessToken, 
                  refreshToken: result.tokens.refreshToken 
                })
              ),
              AsyncStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(transformedUser))
            ])
            
            return { kind: "ok" }
          } else {
            // For non-ok responses, return the kind and any error message
            const errorKind = result.kind
            const errorMessage = 'error' in result ? result.error : undefined
            return { kind: errorKind, error: errorMessage }
          }
        } catch (error) {
          console.error("Failed to login:", error)
          return { kind: "bad-data" }
        } finally {
          self.setProp("isLoading", false)
        }
      },

      async approveUser(code: string) {
        self.setProp("isLoading", true)
        try {
          const result = await api.approveUser(code)
          if (result.kind === "ok") {
            self.setProp("currentUser", {
              ...result.user,
              id: String(result.user.id),
            })
            return { kind: "ok" }
          }
          return { kind: "error", error: result }
        } catch (error) {
          console.error("Failed to approve user:", error)
          return { kind: "error", error: "unknown" }
        } finally {
          self.setProp("isLoading", false)
        }
      },

      async resendVerificationCode() {
        self.setProp("isLoading", true)
        try {
          const result = await api.resendVerificationCode()
          if (result.kind === "ok") {
            return { kind: "ok" }
          }
          return { kind: "error", error: result }
        } catch (error) {
          console.error("Failed to resend verification code:", error)
          return { kind: "error", error: "unknown" }
        } finally {
          self.setProp("isLoading", false)
        }
      },

      async forgotPassword(email: string) {
        try {
          const result = await api.forgotPassword(email);
          if (result.kind === "ok") {
            return { kind: "ok" };
          } else {
            return { kind: "error", error: result.error };
          }
        } catch (error) {
          console.error("Error in forgotPassword:", error);
          return { kind: "error", error: "An unexpected error occurred" };
        }
      },

      async resetPassword(code: string, password: string, confirmPassword: string) {
        try {
          console.log("Resetting password with code:", code, password, confirmPassword)
          const result = await api.resetPassword(code, password, confirmPassword);
          if (result.kind === "ok") {
            return { kind: "ok" };
          } else {
            return { kind: "error", error: result.error };
          }
        } catch (error) {
          console.error("Error in resetPassword:", error);
          return { kind: "error", error: "An unexpected error occurred" };
        }
      },

      async updateProfile(userData: {
        name?: string
        surname?: string
      }) {
        self.setProp("isLoading", true)
        try {
          let result = await api.updateProfile(userData)
          
          // If unauthorized, try to refresh token and retry
          if (result.kind === "unauthorized" || result.kind === "forbidden") {
            const refreshed = await (self as any).refreshAuthToken()
            if (refreshed) {
              result = await api.updateProfile(userData)
            }
          }

          if (result.kind === "ok" && self.currentUser) {
            Object.entries(userData).forEach(([key, value]) => {
              const user = self.currentUser!
              if (value !== undefined && key in user) {
                user.setProp(key as any, value)
              }
            })
            return { kind: "ok" }
          } else {
            return { kind: "error", error: result.kind }
          }
        } catch (error) {
          console.error("Failed to update profile:", error)
          return { kind: "error", error: "unknown" }
        } finally {
          self.setProp("isLoading", false)
        }
      },

      async updateProfilePhoto(photoUri: string) {
        self.setProp("isLoading", true)
        try {
          const result = await api.updateProfilePhoto(photoUri, self.accessToken)
          if (result.kind === "ok" && self.currentUser) {
            self.currentUser.setProp("avatar", result.avatar)
            await AsyncStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(self.currentUser))
            return { kind: "ok" }
          } else {
            return { kind: "error", error: result.kind }
          }
        } catch (error) {
          console.error("Failed to update profile photo:", error)
          return { kind: "error", error: "unknown" }
        } finally {
          self.setProp("isLoading", false)
        }
      },
      async refreshProfile() {
        if (!self.currentUser?.id) return { kind: "error", error: "no-user" }
        
        self.setProp("isLoading", true)
        try {
          const result = await api.getProfile()
          if (result.kind === "ok") {
            self.setProp("currentUser", {
              ...result.user,
              id: String(result.user.id),
            })
            return { kind: "ok" }
          } else {
            return { kind: "error", error: result.kind }
          }
        } catch (error) {
          console.error("Failed to refresh profile:", error)
          return { kind: "error", error: "unknown" }
        } finally {
          self.setProp("isLoading", false)
        }
      },
    }))
  return model
})()

export interface User extends Instance<typeof UserModel> {}
export interface UserStoreType extends Instance<typeof UserStoreModel> {}
export interface UserSnapshotIn extends SnapshotIn<typeof UserModel> {}
export interface UserSnapshotOut extends SnapshotOut<typeof UserModel> {}

