import { ViewStyle, TextStyle } from "react-native"

export const globalButton: ViewStyle = {
  backgroundColor: '#fff',
  borderRadius: 11,
  paddingVertical: 10,
  paddingHorizontal: 20,
  marginTop: 10,
  shadowColor: '#000',
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.2,
  shadowRadius: 2,
  elevation: 2,
}

export const globalButtonText: TextStyle = {
  color: '#000',
  fontSize: 16,
  fontWeight: 'bold',
  textAlign: 'center',
}

export const globalRedButton: ViewStyle = {
  backgroundColor: '#FF0000',
  borderRadius: 11,
  paddingVertical: 10,
  paddingHorizontal: 20,
  marginTop: 10,
  shadowColor: '#000',
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.2,
  shadowRadius: 2,
  elevation: 2,
}

export const globalRedButtonText: TextStyle = {
  color: '#fff',
  fontSize: 16,
  fontWeight: 'bold',
  textAlign: 'center',
}

export const globalGreenButton: ViewStyle = {
  backgroundColor: '#03A700',
  borderRadius: 11,
  paddingVertical: 10,
  paddingHorizontal: 20,
  marginTop: 10,
  shadowColor: '#000',
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.2,
  shadowRadius: 2,
  elevation: 2,
}

export const globalGreenButtonText: TextStyle = {
  color: '#fff',
  fontSize: 16,
  fontWeight: 'bold',
  textAlign: 'center',
}

export const globalBlueButton: ViewStyle = {
  backgroundColor: '#2EC5E3',
  borderRadius: 11,
  paddingVertical: 10,
  paddingHorizontal: 20,
  marginTop: 10,
  shadowColor: '#000',
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.2,
  shadowRadius: 2,
  elevation: 2,
}

export const globalBlueButtonText: TextStyle = {
  color: '#fff',
  fontSize: 16,
  fontWeight: 'bold',
  textAlign: 'center',
}

// Add a global text field style
export const globalTextInput: ViewStyle = {
  borderRadius: 11,
  borderWidth: 1,
  backgroundColor: '#fff',
} 