const en = {
  loginScreen: {
    title: "fiş kolay",
    emailLabel: "Email",
    emailPlaceholder: "Enter your email",
    passwordLabel: "Password",
    passwordPlaceholder: "Enter your password",
    loginButton: "Login",
    appleLogin: "Continue with <PERSON>",
    googleLogin: "Continue with Google",
    registerLink: "Register",
    forgotPassword: "Forgot Password",
    loginError: "<PERSON><PERSON> failed. Please check your credentials.",
  },
  registerScreen: {
    title: "fiş kolay",
    emailLabel: "Email",
    emailPlaceholder: "Enter your email",
    passwordLabel: "Password",
    passwordPlaceholder: "Enter your password",
    confirmPasswordLabel: "Confirm Password",
    confirmPasswordPlaceholder: "Confirm your password",
    createAccount: "Create account",
    haveAccount: "Already have an account?",
    login: "Login",
    forgotPassword: "Forgot Password",
  },
  forgotPasswordScreen: {
    title: "Forgot Password",
    description: "Enter your email and we'll send you a password reset link.",
    emailLabel: "Email",
    emailPlaceholder: "Enter your email",
    submit: "Submit",
    backToLogin: "Back to Login",
  },
  resetPasswordScreen: {
    title: "Reset Password",
    description: "Enter the code we sent you and set your new password.",
    codeLabel: "Code",
    codePlaceholder: "Enter code",
    passwordLabel: "New Password",
    passwordPlaceholder: "Enter new password",
    confirmPasswordLabel: "Confirm Password",
    confirmPasswordPlaceholder: "Confirm new password",
    submit: "Reset Password",
  },
  emailVerificationScreen: {
    title: "Email Verification",
    description: "Enter the verification code sent to your email.",
    codeLabel: "Verification Code",
    codePlaceholder: "Enter code",
    verify: "Verify",
    resendCode: "Resend code",
  },
  successScreen: {
    emailVerificationTitle: "Email Verified",
    emailVerificationDescription: "Your email has been successfully verified.",
    passwordResetTitle: "Password Reset",
    passwordResetDescription: "Your password has been successfully reset.",
    continue: "Continue",
  },
  errorScreen: {
    emailVerificationTitle: "Verification Error",
    emailVerificationDescription: "Email verification failed.",
    passwordResetTitle: "Reset Error",
    passwordResetDescription: "Password reset failed.",
    generalTitle: "Error",
    generalDescription: "Something went wrong.",
    tryAgain: "Try Again",
  },
  common: {
    ok: "OK",
    save: "Save",
    edit: "Edit",
    cancel: "Cancel",
  },
  profileScreen: {
    emailLabel: "Email",
    emailPlaceholder: "Enter your email",
    firstNameLabel: "First Name",
    firstNamePlaceholder: "Enter your first name",
    lastNameLabel: "Last Name",
    lastNamePlaceholder: "Enter your last name",
    phoneLabel: "Phone",
    phonePlaceholder: "Enter your phone number",
    editTitle: "Edit Profile",
  },
  choosePhotoScreen: {
    takePhoto: "Take Photo",
    chooseFromGallery: "Choose from Gallery",
  },
}

export default en

