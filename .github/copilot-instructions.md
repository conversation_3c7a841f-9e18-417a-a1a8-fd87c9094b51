# Copilot Instructions for fish-reader

## Project Overview
This is a React Native app based on the Ignite boilerplate by Infinite Red. The architecture is modular, with clear separation between UI components, navigation, models (stores), services, and configuration. The project supports both iOS and Android platforms and uses Expo EAS for builds.

## Key Directories & Files
- `app/` — Main source code: components, screens, models, navigation, config, theme, utils
- `assets/` — Static assets: `icons/`, `images/`
- `android/`, `ios/` — Native platform code
- `app.ignite/` — Reference Ignite boilerplate (for upgrades/comparisons)
- `test/` — Test files
- `package.json` — Scripts, dependencies
- `README.md` — Quickstart, build/test instructions

## Build & Run
- Install dependencies: `npm install`
- Start Metro bundler: `npm run start`
- Build for iOS simulator: `npm run build:ios:sim`
- Build for iOS device: `npm run build:ios:dev`
- Build for iOS production: `npm run build:ios:prod`
- EAS build required for device/simulator: see README for details

## Testing
- End-to-end tests use Maestro. See [Maestro Setup](https://ignitecookbook.com/docs/recipes/MaestroSetup).
- Unit tests are in `test/` and some model/component files (e.g., `Text.test.tsx`, `Episode.test.ts`).

## Patterns & Conventions
- **Component Structure:** Use functional components, keep styles in `theme/`.
- **Navigation:** Managed in `app/navigators/`.
- **State Management:** MobX stores in `app/models/` (e.g., `UserStore`, `RootStore`).
- **Config:** Environment-specific config in `app/config/`.
- **Assets:** Import images/icons using `require()` from `assets/`.
- **Error Handling:** Use `ErrorModal` and `ErrorScreen` components.
- **Upgrades:** Reference `app.ignite/` for boilerplate updates.

## External Integrations
- Uses Ignite CLI and Expo EAS for builds/deployment.
- Reactotron for debugging (`app/devtools/`).

## Tips for AI Agents
- When adding new screens/components, follow the structure in `app/screens/` and `app/components/`.
- For new models/stores, use MobX patterns as in `app/models/`.
- Update navigation in `app/navigators/` when adding screens.
- Keep styles and theme changes in `app/theme/`.
- Reference `app.ignite/` for original boilerplate code if unsure about conventions.

---
For more details, see `README.md` and links to Ignite documentation. If any section is unclear or missing, ask for clarification or examples from the user.